import { Component, inject, OnInit, signal, ChangeDetectorRef } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MovimentacoesService } from '../../core/services/api/movimentacoes.service';
import { GlobalService } from '../../core/services/global.service';
import { DialogoService } from '../../core/services/dialogos/dialogo.service';
import { MovimentacoesDetalhes } from '../../shared/interfaces/api/movimentacao-detalhes.interface';
import { SharedModule } from '../../shared/shared.module';
import { TabelaExpansivaComponent } from '../../shared/components/tabelas/tabela-expansiva/tabela-expansiva.component';
import { ColDef } from 'ag-grid-community';
import { DocumentosService } from '../../core/services/api/documentos.service';
import { DocumentoTipo } from '../../shared/enums/documento-tipo.enum';
import { CnpjPipe } from '../../shared/pipes/cnpj.pipe';

//Interface para os cards de valores
interface ValorCard {
    titulo: string;
    valor: number;
    icon: string;
    background: string;
}

/**
 * Interface para os documentos
 */
interface Documento {
    id?: number;
    tipo?: TipoDocumento;
    [key: string]: any;
}

interface TipoDocumento {
    descricao: string;
	codigo: number;
}

@Component({
	selector: 'app-movimentacoes-detalhes',
	standalone: true,
	imports: [CommonModule, CurrencyPipe, SharedModule, TabelaExpansivaComponent, CnpjPipe],
	templateUrl: './movimentacoes-detalhes.component.html',
	styleUrls: ['./movimentacoes-detalhes.component.scss'],
})
export class MovimentacoesDetalhesComponent implements OnInit {
    // Estado de carregamento
    isLoading = signal<boolean>(true);
    
    // Informações da movimentação
    idMovimentacao: number | null = null;
    detalhesMovimentacao = signal<MovimentacoesDetalhes | null>(null);
    globalTenantId!: ReturnType<GlobalService['getDadoCompartilhado']>;

    // Disponibiliza o enum DocumentoTipo para o template
    documentoTipo = DocumentoTipo;
    
    // Informações relacionadas à entidades
    infoNotaFiscalVenda = signal<any[]>([]);
    infoDocumentos = signal<any[]>([]);
    infoAgregador = signal<any>(null);
    infoAtivo = signal<any>(null);
    infoOficina = signal<any>(null);
    infoFornecedor = signal<any>(null);
        
    // Controle de UI
    itemExpandidoOutroIdx: number | null = null;
    collapsedSinistro = false;
    isCompleteStatus = false;

	// Dados dos cards de valores para exibição
	valoresCards: ValorCard[] = [
		{
			titulo: 'Total Pago',
			valor: 0,
			icon: 'fa-solid fa-money-bill',
			background: 'bg-gradient-blue',
		},
		{
			titulo: 'Total A Pagar',
			valor: 0,
			icon: 'fa fa-hand-holding-usd',
			background: 'bg-gradient-green',
		},
		{
			titulo: 'Total Ressarcido',
			valor: 0,
			icon: 'fa-solid fa-undo',
			background: 'bg-gradient-purple',
		},
		{
			titulo: 'Total A Ressarcir',
			valor: 0,
			icon: 'fa-solid fa-calendar-xmark',
			background: 'bg-gradient-purple',
		},
	];

	// Lista de documentos relacionados à movimentação
	documentos: Documento[] = []

	//Definição das colunas para a tabela de itens
	dadosTabelaItens: any[] = [];
	colunasTabelaItens!: ColDef[];
	colunasTabelaItensExpansao!: ColDef[];

	//Definição das colunas para a tabela de outros itens
	dadosOutrosItens: any[] = [];
	colunasOutrosItens!: ColDef[];
	colunasOutrosItensExpansao!: ColDef[];
	
	// **** Tabelas Historicos *****
	// Dados
	dadosHistoricoPagamentos = signal<any[]>([]);
	dadosHistoricoRessarcimentos = signal<any[]>([]);

	// colunas
	colunasHistoricosPagamentos!: ColDef[];
	colunasHistoricosRessarcimentos!: ColDef[];
	colunasHistoricosExpansaoPagamentos!: ColDef[];
	colunasHistoricosExpansaoRessarcimentos!: ColDef[];
    
    // Injeção de dependências
    private rota = inject(ActivatedRoute);
    private globalService = inject(GlobalService);
    private router = inject(Router);
    private dialogoService = inject(DialogoService);
    private movimentacoesService = inject(MovimentacoesService);
    private cdr = inject(ChangeDetectorRef);
	private documentosService = inject(DocumentosService);

    constructor() {}

	ngOnInit(): void {
		this.globalTenantId = this.globalService.getDadoCompartilhado();

		this.idMovimentacao = parseInt(this.rota.snapshot.queryParamMap.get('idMovimentacao') || '') || null;

		this.inicializacaoTabelas();
		this.carregaInformacoes();
		
	}

	// Alterna a expansão de itens na visualização
    toggleExpandirOutro(idx: number): void {
        this.itemExpandidoOutroIdx = this.itemExpandidoOutroIdx === idx ? null : idx;
    }
    
    // Formata um valor para exibição como moeda brasileira (BRL)
    formatarValorMonetario = (params: any): string => {
        return typeof params.value === 'number' 
            ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) 
            : '';
    }
    
    // Renderiza ícone para coluna de divergências
    renderizarColunaDivergencias = (params: any): HTMLElement => {
        const button = document.createElement('span');
        button.style.cursor = 'pointer';
        button.style.fontSize = '16px';
        button.style.color = '#caa220ff';

        if (params.data.divergencias && params.data.divergencias.length > 0) {
            button.className = 'feather icon-alert-triangle text-danger';
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                // this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
            });
        } else {
            button.className = 'feather icon-check text-success';
        }

        return button;
    }	
	
	solicitarDadosBancarios(){
		// Chamar rota
	}

	// Carrega todas as informações necessárias para a exibição dos detalhes da movimentação
	async carregaInformacoes(): Promise<void> {
		this.isLoading.set(true);
		
		try {
			// Carrega os dados da API
			const dadosDetalhes = await this.carregarDadosDetalhes();
			if (!dadosDetalhes) return;
			
			// Processa os dados carregados
			this.processarDadosDetalhes(dadosDetalhes);
		} catch (erro) {
			console.error('Erro ao processar detalhes da movimentação:', erro);
		} finally {
			this.isLoading.set(false);
		}
	}
	
	/**
	 * Carrega os dados de detalhes da movimentação da API
	 */
	private async carregarDadosDetalhes(): Promise<MovimentacoesDetalhes | null> {
		try {
			const dadosDetalhes = await firstValueFrom(
				this.movimentacoesService.obterMovimentacoesDetalhes(this.idMovimentacao, this.globalTenantId())
			);

			const dadosOutrosItens = await firstValueFrom(
				this.movimentacoesService.obterTodosItensMovimentacao(dadosDetalhes.idAgregador, this.idMovimentacao, this.globalTenantId())
			);

			if (dadosOutrosItens) {
				this.dadosOutrosItens = dadosOutrosItens;
			}

			if (dadosDetalhes) {
				this.detalhesMovimentacao.set(dadosDetalhes);
				return dadosDetalhes;
			} else {
				this.detalhesMovimentacao.set(null);
				console.warn('Nenhum detalhe encontrado para o ID:', this.idMovimentacao);
				return null;
			}
			
		} catch (erro) {
			console.error('Erro ao carregar detalhes da movimentação:', erro);
			this.detalhesMovimentacao.set(null);
			return null;
		}
	}
	
	/**
	 * Processa os dados de detalhes carregados e atualiza os componentes visuais
	 */
	private processarDadosDetalhes(dadosDetalhes: MovimentacoesDetalhes): void {
		// Atualiza info do agregador
		this.atualizarInfoAgregador(dadosDetalhes);
		
		// Atualiza valores dos cards
		this.atualizarValoresCards(dadosDetalhes.valoresConsolidados || {});
		
		// Atualiza informações relacionadas
		this.atualizarInfoEntidades(dadosDetalhes);
		
		// Atualiza históricos
		this.atualizarHistoricos(dadosDetalhes);
		
		// Processa documentos
		this.processarDocumentos(dadosDetalhes.documentos || []);
		
		// Processa dados para tabela
		this.processarDadosParaTabela(dadosDetalhes);
	}
	
	/**
	 * Atualiza as informações do agregador
	 */
	private atualizarInfoAgregador(dadosDetalhes: MovimentacoesDetalhes): void {
		this.infoAgregador.set({
			numero: dadosDetalhes?.numeroSinistro,
			id: dadosDetalhes?.idAgregador
		});
	}
	
	/**
	 * Atualiza os valores exibidos nos cards
	 */
	private atualizarValoresCards(valoresConsolidados: any): void {
		this.valoresCards.forEach((item) => {
			switch (item.titulo) {
				case 'Total Pago':
					item.valor = valoresConsolidados?.pago || 0;
					break;
				case 'Total A Pagar':
					item.valor = valoresConsolidados?.aPagar || 0;
					break;
				case 'Total Ressarcido':
					item.valor = valoresConsolidados?.ressarcido || 0;
					break;
				case 'Total A Ressarcir':
					item.valor = valoresConsolidados?.aRessarcir || 0;
					break;
			}
		});
	}
	
	/**
	 * Atualiza as informações relacionadas às entidades (ativo, oficina, fornecedor)
	 */
	private atualizarInfoEntidades(dadosDetalhes: MovimentacoesDetalhes): void {
		// Atualiza info do ativo
		this.infoAtivo.set(dadosDetalhes.ativo || null);
		
		// Atualiza info da oficina
		this.infoOficina.set(dadosDetalhes.oficina || null);
		
		// Atualiza info do fornecedor
		this.infoFornecedor.set(dadosDetalhes.fornecedor || null);
	}
	
	/**
	 * Atualiza os históricos de pagamentos e ressarcimentos
	 */
	private atualizarHistoricos(dadosDetalhes: MovimentacoesDetalhes): void {
		// Histórico de pagamentos
		this.dadosHistoricoPagamentos.set(
			Array.isArray(dadosDetalhes.pagamentos) ? dadosDetalhes.pagamentos : []
		);
		
		// Histórico de ressarcimentos
		this.dadosHistoricoRessarcimentos.set(
			Array.isArray(dadosDetalhes.ressarcimentos) ? dadosDetalhes.ressarcimentos : []
		);
	}
	
	// Processa os documentos relacionados à movimentação
	private processarDocumentos(documentos: any[]): void {
		// Ordena documentos conforme o enum DocumentoTipo (ordem crescente)
		const documentosOrdenados = [...documentos].sort((a, b) => {
			// Garante que estamos comparando por código
			const codigoA = a.tipo?.codigo || a.tipo || 0;
			const codigoB = b.tipo?.codigo || b.tipo || 0;
			return codigoA - codigoB;
		});
		
		// Atualiza lista completa de documentos ordenados
		this.infoDocumentos.set(documentosOrdenados);
		
		// Limpa a lista atual de documentos
		this.documentos = [];
		
		// Filtra documentos por tipo
		documentosOrdenados.forEach(item => {
			if ((item.tipo?.codigo === DocumentoTipo.NotaFiscalVenda || 
				item.tipo?.codigo === DocumentoTipo.NotaFiscalServico) || 
				(item.tipo === DocumentoTipo.NotaFiscalVenda || 
				item.tipo === DocumentoTipo.NotaFiscalServico)) {
				this.documentos.push({ ...item });
			}
		});
	}
	
	// Processa os dados para exibição na tabela
	private processarDadosParaTabela(dadosDetalhes: MovimentacoesDetalhes): void {
		if (dadosDetalhes.itens && Array.isArray(dadosDetalhes.itens)) {
			this.dadosTabelaItens = dadosDetalhes.itens.map((item: any) => {
				const versao = item.versoes && item.versoes.length > 0 ? item.versoes[0] : {};
				return {
					nome: versao.fornecedor?.razaoSocial || (dadosDetalhes.fornecedor?.razaoSocial ?? ''),
					partNumber: item.codigo,
					valor: versao.valorTotal ?? 0,
					movimentacao: versao.tipoMovimento ?? '',
					item: item.descricao,
					dataAutorizacao: versao.dataAutorizacao ?? '',
					versoes: item.versoes || [],
				};
			});
			this.cdr.detectChanges();
		} else {
			this.dadosTabelaItens = [];
		}
	}

	// Solicita atualização de status da oficina e redireciona para validação
	solicitarStatusOficina(): void {
		const configDialogo = {
			titulo: 'Solicitação enviada!',
			descricao: 'Email de confirmação de status do veículo foi enviado para oficina com sucesso.',
			mostrarBotaoCancelar: false,
			botaoConfirmar: {
				texto: 'OK',
				classe: 'btn-primary',
				icone: 'fa fa-check',
				habilitaIcone: true,
			},
		};
		
		this.dialogoService.dialogoNotificacao(configDialogo).subscribe({
			next: (resultado) => {
				if (resultado) {
					this.redirecionarParaValidacaoStatus();
				}
			},
			error: (erro) => {
				console.error('Erro ao solicitar status da oficina:', erro);
			},
		});
	}
	
	/**
	 * Redireciona para a tela de validação de status do veículo
	 */
	private redirecionarParaValidacaoStatus(): void {
		if (!this.infoAtivo() || !this.infoAtivo()[0]?.id) {
			console.error('ID do ativo não encontrado');
			return;
		}
		
		this.router.navigate(['/validacao-codigo/atualizacao-status-veiculo'], {
			queryParams: {
				idAtivo: this.infoAtivo()[0].id,
				idAgregador: this.infoAgregador().numero || '',
			},
		});
	}

	/**
	 * Solicita ressarcimento ao fornecedor e redireciona para validação
	 */
	solicitarRessarcimento(): void {
		const configDialogo = {
			titulo: 'Solicitação enviada!',
			descricao: 'Email de ressarcimento do valor da peça foi enviado para o fornecedor com sucesso.',
			mostrarBotaoCancelar: false,
			botaoConfirmar: {
				texto: 'OK',
				classe: 'btn-primary',
				icone: 'fa fa-check',
				habilitaIcone: true,
			},
		};
		
		this.dialogoService.dialogoNotificacao(configDialogo).subscribe({
			next: (resultado) => {
				if (resultado) {
					this.redirecionarParaValidacaoRessarcimento();
				}
			},
			error: (erro) => {
				console.error('Erro ao solicitar ressarcimento:', erro);
			},
		});
	}
	
	// Redireciona para a tela de validação de ressarcimento
	private redirecionarParaValidacaoRessarcimento(): void {
		const valorRessarcimento = this.valoresCards.find(card => card.titulo === 'Total A Ressarcir')?.valor || 0;
		
		this.router.navigate(['/validacao-codigo/pagamento-ressarcimento'], {
			queryParams: {
				valor: valorRessarcimento,
				idAgregador: this.idMovimentacao,
			},
		});
	}


	baixarDocumento(documentoId: number): void {
		if (!documentoId) {
			console.error('ID do documento não fornecido');
			return;
		}

		this.documentosService.baixar(this.globalTenantId(), documentoId).subscribe({
			next: (response) => {			
				const blob = new Blob([response], { type: 'application/octet-stream' });
				const url = window.URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `documento_${documentoId}.pdf`;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			},
			error: (erro) => {
				console.error('Erro ao baixar documento:', erro);
			},
		});
	}

	inicializacaoTabelas(){

		this.colunasTabelaItens = [
			{ field: 'partNumber', headerName: 'Código' },
			{ field: 'item', headerName: 'Descrição' },
			{
				field: 'valor',
				headerName: 'Valor Atualizado',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
					typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			},
			{
				field: 'dataAutorizacao',
				headerName: 'Data Autorização',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{
				field: 'divergencias',
				headerName: 'Divergências',
				cellRenderer: (params: any) => {
					const button = document.createElement('span');
					button.style.fontSize = '16px';
					button.style.color = '#f73333ff';
					let temDivergencias = params.data.versoes.some((element: any) => element.divergencias && element.divergencias.length > 0);

					if (temDivergencias) {
						button.className = 'feather icon-alert-triangle text-danger';
					} else {
						button.className = 'feather icon-check text-success';
					}

					return button;
				},
			},
			{
				field: 'condicoes',
				headerName: 'Condições',
				cellRenderer: (params: any) => {
					const button = document.createElement('span');
					button.style.fontSize = '16px';
					button.style.color = '#caa220ff';

					let temCondicoes = params.data.versoes.some((element: any) => element.condicoes && element.condicoes.length > 0);

					if (temCondicoes) {
						button.className = 'feather icon-alert-triangle';
					} else {
						button.className = 'feather icon-check text-success';
					}

					return button;
				},
			},
			

		];

		this.colunasTabelaItensExpansao = [
			{
				field: 'tipoMovimento',
				headerName: 'Tipo Movimento',
				cellRenderer: (params: any) => {
					const span = document.createElement('span');
					span.textContent = params.data.tipoMovimento;

					if (params.data.tipoMovimento === 'Autorização') {
						span.style.color = '#20ca2eff';
					} else {
						span.style.color = '#f73333ff';
					}
					return span;
				},
			},
			{
				field: 'dataAutorizacao',
				headerName: 'Data Autorização',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;
					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ field: 'quantidade', headerName: 'Quantidade', cellStyle: { textAlign: 'center' } },
			{
				field: 'valorTotal',
				headerName: 'Valor Total',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
					typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			},
			{ 	field: 'documentos', 
				headerName: 'Documento(s)', 
				cellStyle: { textAlign: 'center' } ,
				cellRenderer: (params: any) => {
					const div = document.createElement('div');
					let documentosArr: string[] = [];

					params.data.documentos.forEach((item: any) => {
						documentosArr.push(item.numero);
					});

					let documentosAjustados: string[] = [];

					if (Array.isArray(documentosArr)) {
						documentosAjustados = documentosArr.map((s: string) => s.trim());
					}

					documentosAjustados.forEach((item) => {
						const span = document.createElement('span');
						span.textContent = item;
						span.style.display = 'block';
						div.appendChild(span);
					});

					if (documentosArr.length === 0) {
						const span = document.createElement('span');
						span.textContent = '-';
						div.appendChild(span);
					}

					return div;
				},
			
			},
			{ field: 'dataEntrega', headerName: 'Data Entrega' },
			{
				field: 'divergencias',
				headerName: 'Divergências',
				cellClass: 'text-center',
				cellRenderer: (params: any) => {
					const div = document.createElement('div');
					let divergenciasArr: string[] = [];
					if (Array.isArray(params.data.divergencias)) {
						divergenciasArr = params.data.divergencias;
					} else if (typeof params.data.divergencias === 'string') {
						divergenciasArr = params.data.divergencias.split(',').map((s: string) => s.trim());
					}
					divergenciasArr.forEach((item) => {
						const span = document.createElement('span');
						span.textContent = item;
						span.style.display = 'block';
						span.style.color = '#f73333ff';
						div.appendChild(span);
					});
					if (divergenciasArr.length === 0) {
						const span = document.createElement('span');
						span.textContent = '-';
						div.appendChild(span);
					}
					return div;
				},
			},
			{
				field: 'condicoes',
				headerName: 'Condições',
				cellClass: 'text-center',
				cellRenderer: (params: any) => {
					const div = document.createElement('div');
					let divergenciasArr: string[] = [];
					if (Array.isArray(params.data.condicoes)) {
						divergenciasArr = params.data.condicoes;
					} else if (typeof params.data.condicoes === 'string') {
						divergenciasArr = params.data.condicoes.split(',').map((s: string) => s.trim());
					}
					divergenciasArr.forEach((item) => {
						const span = document.createElement('span');
						span.textContent = item;
						span.style.display = 'block';
						span.style.color = '#caa220ff';
						div.appendChild(span);
					});
					if (divergenciasArr.length === 0) {
						const span = document.createElement('span');
						span.textContent = '-';
						div.appendChild(span);
					}
					return div;
				},
			},
		];

		this.colunasOutrosItens = [
			{ field: 'codigo', headerName: 'Código', minWidth: 120 },
			{ field: 'descricao', headerName: 'Descrição', minWidth: 120 },
			{ 
				field: 'valor', 
				headerName: 'Valor Atualizado', 
				minWidth: 120, 
				cellStyle: { textAlign: 'center' },
				valueFormatter: this.formatarValorMonetario,
			},
			{
				field: 'divergencias',
				headerName: 'Divergências',
				cellRenderer: this.renderizarColunaDivergencias,
				minWidth: 100,
				sortable: false,
				filter: false,
			},
		];

		this.colunasOutrosItensExpansao = [
				   { 
					   headerName: 'Fornecedor', 
					   cellStyle: { textAlign: 'center' },
					   cellRenderer: (params: any) => {
						   const div = document.createElement('div');
						   div.textContent = params.data.fornecedor?.nomeFantasia || '-';
						   return div;
					   }
				   },
			{
				field: 'tipoMovimento',
				headerName: 'Tipo Movimento',
				cellRenderer: (params: any) => {
					const span = document.createElement('span');
					span.textContent = params.data.tipoMovimento;

					if (params.data.tipoMovimento === 'Autorização') {
						span.style.color = '#20ca2eff';
					} else {
						span.style.color = '#f73333ff';
					}
					return span;
				},
			},
			{
				field: 'dataAutorizacao',
				headerName: 'Data Autorização',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;
					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ field: 'quantidade', headerName: 'Quantidade', cellStyle: { textAlign: 'center' } },
			{
				field: 'valorTotal',
				headerName: 'Valor Total',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
					typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			},
			{ 
				field: 'documentoIds', 
				headerName: 'Documento(s)', 
				cellStyle: { textAlign: 'center' },
				cellRenderer: (params: any) => {
					const div = document.createElement('div');
					div.textContent = params.data.documentoIds?.join(', ') || '-';
					return div;
				}
			},
			{ 
				field: 'dataAutorizacao', 
				headerName: 'Data Entrega',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;
					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{
				field: 'divergencias',
				headerName: 'Divergências',
				cellClass: 'text-center',
				cellRenderer: (params: any) => {
					const div = document.createElement('div');
					let divergenciasArr: string[] = [];
					if (Array.isArray(params.data.divergencias)) {
						divergenciasArr = params.data.divergencias;
					} else if (typeof params.data.divergencias === 'string') {
						divergenciasArr = params.data.divergencias.split(',').map((s: string) => s.trim());
					}
					divergenciasArr.forEach((item) => {
						const span = document.createElement('span');
						span.textContent = item;
						span.style.display = 'block';
						span.style.color = '#f73333ff';
						div.appendChild(span);
					});
					if (divergenciasArr.length === 0) {
						const span = document.createElement('span');
						span.textContent = '-';
						div.appendChild(span);
					}
					return div;
				},
			},
			
		];

		this.colunasHistoricosPagamentos = [
			{ 
				field: 'dataCriacao', 
				headerName: 'Data de criação', 
				minWidth: 30,
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},

			},
			{ 
				field: 'valorTotal', 
				headerName: 'Valor Total', 
				minWidth: 30, 
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
						typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '-',
			},
			{ 
				headerName: 'Valor a Pagar', 
				minWidth: 30, 
				cellStyle: { textAlign: 'center',  },
				cellRenderer: (params: any) => {
					const span = document.createElement('span');
					span.textContent = params.data.valorAPagar ? params.data.valorAPagar.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '-';
					span.style.color = '#f73333ff';
					
					return span;
				},
			},
			{
				field: 'dataProgramada',
				headerName: 'Data Programada',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ 
				field: 'situacao', 
				headerName: 'Situação', 
				minWidth: 20, 
				cellClass: 'text-center',
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},

			},
			// { field: 'acao', headerName: 'Ação' },
		];

		this.colunasHistoricosExpansaoPagamentos = [
			{ 
				field: 'dataPagamento', 
				headerName: 'Data do pagamento', 
				minWidth: 80, 
				cellClass: 'text-center',
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ 
				field: 'valor', 
				headerName: 'Valor', 
				minWidth: 80, 
				cellClass: 'text-center',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
						typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			},
			{ 
				field: 'comprovante', 
				headerName: 'Comprovante', 
				cellClass: 'text-center',
				minWidth: 80,
				cellRenderer: () => {
					const button = document.createElement('a');
					button.style.fontSize = '16px';
					button.style.color = '#04a9f5';
					button.style.cursor = 'pointer';
					button.className = 'feather icon-download';

					button.addEventListener('click', (e) => {
						e.stopPropagation();
						// this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
					});
					

					return button;
				},

			}
		];

		this.colunasHistoricosRessarcimentos = [
			{ 
				field: 'dataCriacao', 
				headerName: 'Data de criação', 
				minWidth: 30,
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},

			},
			{ 
				field: 'valorTotal', 
				headerName: 'Valor Total', 
				minWidth: 30, 
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
						typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '-',
			},
			{ 
				headerName: 'Valor a Ressarcir', 
				minWidth: 30, 
				cellStyle: { textAlign: 'center',  },
				cellRenderer: (params: any) => {
					const span = document.createElement('span');
					span.textContent = params.data.valorARessarcir ? params.data.valorARessarcir.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '-';
					span.style.color = '#f73333ff';
					
					return span;
				},
			},
			{
				field: 'dataProgramada',
				headerName: 'Data Programada',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ 
				field: 'situacao', 
				headerName: 'Situação', 
				minWidth: 20, 
				cellClass: 'text-center',
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},

			}
		];

		this.colunasHistoricosExpansaoRessarcimentos = [
			{ 
				field: 'dataRessarcimento', 
				headerName: 'Data do Ressarcimento', 
				minWidth: 80, 
				cellClass: 'text-center',
				valueFormatter: (params) => {
					if (!params.value) return '';
					const date = new Date(params.value);
					if (isNaN(date.getTime())) return params.value;

					return date.toLocaleDateString('pt-BR', { timeZone: 'America/Sao_Paulo' }).replace(',', '');
				},
			},
			{ 
				field: 'valor', 
				headerName: 'Valor', 
				minWidth: 80, 
				cellClass: 'text-center',
				cellStyle: { textAlign: 'center' },
				valueFormatter: (params) =>
						typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			},
			{ 
				field: 'comprovante', 
				headerName: 'Comprovante', 
				cellClass: 'text-center',
				minWidth: 80,
				cellRenderer: () => {
					const button = document.createElement('a');
					button.style.fontSize = '16px';
					button.style.color = '#04a9f5';
					button.style.cursor = 'pointer';
					button.className = 'feather icon-download';

					button.addEventListener('click', (e) => {
						e.stopPropagation();
						// this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
					});
					

					return button;
				},

			}
		];
	}
}
