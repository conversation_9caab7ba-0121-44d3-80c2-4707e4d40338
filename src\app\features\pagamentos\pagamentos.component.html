<div class="card">
	<div class="card-header">
		<div class="d-flex justify-content-between align-items-center">
			<h5 class="card-title mb-0">Pagamentos</h5>
			<div class="action-buttons">
				<button class="btn btn-outline-secondary btn-sm" (click)="abrirDialogoImportar()">
					<i class="feather icon-upload"></i>
					Importar
				</button>
			</div>
		</div>
	</div>

	<div class="row mb-2 p-4">
		@for (filtro of filtrosApi; track filtro) {
			<div class="col-3">
				<label>Filtrar {{ filtro.label }}</label>
				<ng-container>
					<input
						type="text"
						class="form-control form-control-sm mb-4"
						[(ngModel)]="filtrosApi[$index].valor"
						placeholder="{{ filtro.label }}"
					/>
				</ng-container>
			</div>
		}
	</div>

	@if (erro()) {
		<div class="alert alert-danger">
			{{ erro() }}
		</div>
	}

	<div class="p-4">
		@if (showGrid) {
			<app-tabela-padrao
				[columnDefs]="columnDefs"
				[pagination]="true"
				[rowData]="dadosTabela()"
				(gridReady)="onGridReady()" />
		}
	</div>
</div>
