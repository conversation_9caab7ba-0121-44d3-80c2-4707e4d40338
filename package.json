{"name": "zin-frontend-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json", "start:prod": "ng serve --configuration production --proxy-config proxy.conf.json", "build": "ng build --configuration production", "build:proxy": "ng build --configuration production --proxy-config proxy.conf.json", "build:mock": "ng build --configuration mock", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:zin-frontend-angular": "node dist/zin-frontend-angular/server/server.mjs", "format": "prettier --write \"src/**/*.{ts,html,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,html,scss}\""}, "prettier": {"printWidth": 150, "tabWidth": 4, "useTabs": true, "semi": true, "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "chainOperatorBreakBefore": false, "methodChainBreakBefore": true, "overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.0.6", "@angular/cdk": "^20.0.5", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/localize": "^20.1.0", "@angular/material": "^20.0.5", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0", "@angular/ssr": "^20.0.5", "@fortawesome/fontawesome-free": "^7.0.0", "@ng-bootstrap/ng-bootstrap": "19.0.0", "@popperjs/core": "2.11.8", "ag-grid-angular": "^34.1.1", "ag-grid-community": "^34.1.1", "apexcharts": "^5.3.3", "bootstrap": "5.3.7", "chart.js": "^4.5.0", "express": "^5.1.0", "jwt-decode": "^4.0.0", "ng-apexcharts": "^2.0.0", "ng-multiselect-dropdown": "^1.0.0", "ng2-charts": "^5.0.4", "ngx-mask": "^20.0.3", "ngx-scrollbar": "18.0.0", "rxjs": "~7.8.2", "screenfull": "6.0.2", "tslib": "2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.5", "@angular-eslint/builder": "^20.1.1", "@angular-eslint/eslint-plugin": "^20.1.1", "@angular-eslint/schematics": "^20.1.1", "@angular-eslint/template-parser": "^20.1.1", "@angular/build": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.6.2", "typescript": "~5.8.2"}}