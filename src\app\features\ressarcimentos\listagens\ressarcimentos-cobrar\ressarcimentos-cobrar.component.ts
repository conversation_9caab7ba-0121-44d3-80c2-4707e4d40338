import { Component, inject, signal } from '@angular/core';
import { TabelaPadraoComponent } from "../../../../shared/components/tabelas/tabela-padrao/tabela-padrao.component";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Ressarcimento } from '../../../../shared/interfaces/api/ressarcimento.interface';
import { DialogoService } from '../../../../core/services/dialogos/dialogo.service';
import { ColDef } from 'ag-grid-community';
import { Router } from '@angular/router';
import { TipoDialogoDescList } from '../../../../shared/enums/dialogos/tipo-dialogo-descricao-list.enum';
import { ConfiguracaoDialogoLista, DialogoDescricaoListaUtil } from '../../../../shared/utils/dialogo-descricao-lista.util';
import { RessarcimentoService } from '../../../../core/services/api/ressarcimento.service';

@Component({
  selector: 'app-ressarcimentos-cobrar',
  imports: [TabelaPadraoComponent, CommonModule, FormsModule],
  templateUrl: './ressarcimentos-cobrar.component.html',
  styleUrl: './ressarcimentos-cobrar.component.scss'
})
export class RessarcimentosCobrarComponent {
   	erro = signal('');

	showGrid: boolean = true;
    
	private ressarcimentosService = inject(RessarcimentoService);

	ngOnInit() {
		this.ressarcimentosService.listarRessarcimentosCobrados().subscribe({
			next: (dados: Ressarcimento[]) => {
				this.dadosTabela.set(dados);
			},
			error: (err) => {
				this.erro.set('Erro ao carregar dados');
			}
		});
	}

/* 	public filtrosApi: { label: string; valor: string | string[] }[] = [
		{ label: 'Nº Sinistro', valor: '' },
		{ label: 'Fornecedor', valor: '' },
		{ label: 'Data Autorização', valor: '' },
		{ label: 'NF', valor: '' },
		{ label: 'Situação', valor: '' },
		{ label: 'Ressarcir', valor: '' },
		{ label: 'Ressarcido', valor: '' },

	]; */

	public columnDefs: ColDef[] = [
		{ field: 'agregador', headerName: 'Nº Sinistro', minWidth: 120 },
		{ field: 'fornecedor', headerName: 'Fornecedor', minWidth: 120 },
		{ field: 'dataAutorizacao', headerName: 'Data Autorização', minWidth: 120 },
		{ field: 'numeroDocumento', headerName: 'NF', minWidth: 120 },
		{ field: 'valorTotal', headerName: 'Total', minWidth: 120 },
		{ field: 'pago', headerName: 'Pago', minWidth: 120 },
		{ field: 'aPagar', headerName: 'A Pagar', minWidth: 120 },
		{ field: 'ressarcir', headerName: 'Ressarcir', minWidth: 120 },
		{ field: 'ressarcido', headerName: 'Ressarcido', minWidth: 120 },
		{ field: 'placa', headerName: 'Placa', minWidth: 120 },
		{ field: 'statusReparo', headerName: 'Status Reparo', minWidth: 120 },
		{
			headerName: 'Divergências',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';
		
				if (params.data.divergencia === true) {
					button.className = 'feather icon-alert-triangle text-warning';
					button.addEventListener('click', (e) => {
						e.stopPropagation();
						this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
					});
				} else {
					button.className = 'feather icon-check text-success';
				}
					return button;
				},
				minWidth: 100,
				sortable: false,
				filter: false,
		},
		{
					headerName: 'Condições',
					cellRenderer: (params: any) => {
						const button = document.createElement('span');
						button.style.cursor = 'pointer';
						button.style.fontSize = '16px';
						button.style.color = '#818181ff';
		
						if (params.data.condicao === true) {
							button.className = 'feather icon-alert-triangle text-danger';
							button.addEventListener('click', (e) => {
								e.stopPropagation();
								this.abrirDialogoNotificacao(TipoDialogoDescList.condicoes, params.data.condicoes);
							});
						} else {
							button.className = 'feather icon-check text-success';
						}
		
						return button;
					},
					minWidth: 100,
					sortable: false,
					filter: false,
				},
			{
			headerName: 'Detalhes',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.className = 'feather icon-file-text';
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';
				button.addEventListener('click', (e) => {
					e.stopPropagation();
					this.navegaParaDetalhes({ data: params.data });
				});

				return button;
			},
			maxWidth: 120,
			sortable: false,
			filter: false,
		},
		

	];

	dadosTabela = signal<Ressarcimento[]>([]);
	carregando = signal(false);
	//erro = signal('');
	dadosRessarcimento = signal<Ressarcimento[]>([]);
	globalRessarcimentos = inject(DialogoService)
	private dialogoService = inject(DialogoService);
	private router = inject(Router);

	navegaParaDetalhes(event: any) {
		this.router.navigate(['/movimentacoes/detalhes'], {
			queryParams: { idMovimentacao: event.data.idMovimentacao },
		});
	}

	public abrirDialogoNotificacao(tipo: TipoDialogoDescList, itens: any[]): void {
			const titulo = DialogoDescricaoListaUtil.obterTituloPadrao(tipo);
			const configCss: Partial<ConfiguracaoDialogoLista> = DialogoDescricaoListaUtil.obterConfiguracaoPadrao(tipo) || {};
			const descricao = itens;
	
			this.dialogoService
				.dialogoNotificacaoLista({
					titulo,
					descricao,
					configCss,
					botaoConfirmar: { texto: 'Fechar', classe: 'btn btn-primary' },
				})
				.subscribe();
		}

	onGridReady() {
		// Retorno da tabela
	}



}
