import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, catchError, throwError, of } from 'rxjs';
import { Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { environment } from '../../../../environments/environment';
import { jwtDecode } from 'jwt-decode';
import {
	DadosUsuario,
	DadosJwt,
	LoginRequest,
	LoginResponse,
	RecuperarSenhaRequest,
	RecuperarSenhaResponse,
	novaSenhaRequest,
	ConfirmacaoEmailResponse,
	verificaCodigoEmailRequest,
	verificaCodigoEmailResponse,
	refrashTokenRequest,
	ConfirmacaoEmailRequest,
	ConviteNovoUsuarioRequest,
	CadastroNovoUsuarioRequest,
	
} from '../../../shared/interfaces/auth';
import { DialogoService } from '../../services/dialogos/dialogo.service';
import { DadosDialogoNotificacao } from '../../../shared/interfaces/dialogos/dialogo-notificacao.interface';

@Injectable({
	providedIn: 'root',
})
export class AuthService {
	// Configuração de API com proxy para contornar CORS em desenvolvimento
	private readonly API_BASE: string;
	private readonly TOKEN_KEY = 'zin_token';
	private readonly USER_KEY = 'zin_user';
	private readonly SESSION_EXPIRY_KEY = 'zin_session_expiry';
	private readonly SESSION_DURATION = Date.now() + 2 * 60 * 60 * 1000; // 2 horas em milissegundos
	public readonly usaMock: boolean = environment.useMock;
	// private readonly SESSION_DURATION = 10000; // 10 segundos para teste 
	private readonly httpOptions = {
		headers: {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
		},
	};
	private platformId = inject(PLATFORM_ID);
	private navegadorAtivo = isPlatformBrowser(this.platformId);
	private sessionCheckInterval: any;

	private usuarioAtualSubject = new BehaviorSubject<DadosUsuario | null>(null);
	public usuarioAtual$ = this.usuarioAtualSubject.asObservable();

	private estaAutenticadoSubject = new BehaviorSubject<boolean>(this.sessaoValida());
	public estaAutenticado$ = this.estaAutenticadoSubject.asObservable();

	public sessaoUsuario: LoginResponse | null = null;

	constructor(
		private http: HttpClient,
		private router: Router,
		private dialogoSerivce: DialogoService
	) {
		this.API_BASE = this.getApiBase();
		this.carregaDadosDaSessao();
	}

	private getApiBase(): string {
		const apiBase = environment.useProxy ? '/api-cors/auth' : environment?.apiUrlAuth;
		return apiBase;
	}

	login(dadosLogin: LoginRequest): Observable<LoginResponse> {
		return this.http.post<LoginResponse>(`${this.API_BASE}/login`, dadosLogin).pipe(
			tap(async (response) => {
				this.sessaoUsuario = response;

				if (response && !response?.result?.twoFactorEnabled) {
					await this.setSession(response);
					this.iniciaValidadeSessao();
				}
			}),
			catchError((error) => this.handleError(error))
		);
	}

	logout(): Observable<any> {
		return this.http.post(`${this.API_BASE}/logout`, {}, this.getAuthHttpOptions()).pipe(
			tap(() => this.limparSessao()),
			catchError((error) => {
				this.limparSessao();
				return throwError(() => error);
			})
		);
	}

	enviarConviteNovoUsuario(dadosNovoUsuario: ConviteNovoUsuarioRequest): Observable<any> {
		return this.http.post<any>(`${this.API_BASE}/register-user-client`, dadosNovoUsuario)
	}

	cadastroNovoUsuario(email: CadastroNovoUsuarioRequest): Observable<any> {
		return this.http.post<any>(`${this.API_BASE}/confirm-email-set-password`, email).pipe(
			tap(async (response) => {
				this.sessaoUsuario = response;

				if (response && !response?.result?.twoFactorEnabled) {
					await this.setSession(response);
					this.iniciaValidadeSessao();
				}
			}),
			catchError((error) => this.handleError(error))
		);
	}

	recuperarSenha(dadosRecuperar: RecuperarSenhaRequest): Observable<RecuperarSenhaResponse> {
		return this.http.post<RecuperarSenhaResponse>(`${this.API_BASE}/forgot-password`, dadosRecuperar).pipe(
			tap((response) => { }),
			catchError((error) => {
				return throwError(() => error);
			})
		);
	}

	novaSenha(dadosNovaSenha: novaSenhaRequest): Observable<LoginResponse> {
		return this.http.post<LoginResponse>(`${this.API_BASE}/reset-password`, dadosNovaSenha).pipe(
			tap((response) => { }),
			catchError((error) => {
				return throwError(() => error);
			})
		);
	}

	confirmacaoEmail(dadosConfirmacao: ConfirmacaoEmailRequest): Observable<ConfirmacaoEmailResponse | null> {
		if (this.sessaoUsuario && this.sessaoUsuario.result.twoFactorEnabled) {
			return this.http.post<ConfirmacaoEmailResponse>(`${this.API_BASE}/send-2fa-code`, dadosConfirmacao).pipe(
				tap((response) => { }),
				catchError((error) => this.handleError(error))
			);
		}

		return of(null);
	}

	verificaCodigoEmail(dadosDoisFatores: verificaCodigoEmailRequest): Observable<LoginResponse> {
		return this.http.post<LoginResponse>(`${this.API_BASE}/verify-2fa-code`, dadosDoisFatores).pipe(
			tap(async (response) => {

				this.sessaoUsuario = response;

				if (response && response?.result?.twoFactorEnabled) {
					await this.setSession(response);
					this.iniciaValidadeSessao();
				} else if (response.success) {
					this.setSession(this.sessaoUsuario);
					this.iniciaValidadeSessao();
				}

			}),
			catchError((error) => this.handleError(error))
		);
	}

	atualizarToken(dadosAtualizarToken: refrashTokenRequest): Observable<LoginResponse> {
		return this.http.post<LoginResponse>(`${this.API_BASE}/refresh`, dadosAtualizarToken).pipe(
			tap((response) => {
				if (response.success) {
					this.setSession(response);
					this.iniciaValidadeSessao();
				}

			}),
			catchError((error) => this.handleError(error))
		);
	}

	private getAuthHttpOptions() {
		const token = this.getToken();

		return {
			headers: {
				...this.httpOptions.headers,
				...(token ? { Authorization: `Bearer ${token}` } : {}),
			},
		};
	}

	public setSession(authResult: LoginResponse | verificaCodigoEmailResponse | null): void {

		if (this.navegadorAtivo && authResult?.result?.token) {
			try {
				const tokenDecodificado = this.decodificarToken(authResult.result.token);

				if (tokenDecodificado) {
					const dadosUsuario: DadosUsuario = {
						token: authResult.result.token,
						refreshToken: authResult.result.refreshToken,
						expiration: authResult.result.expiration,
						// Informações do token decodificado
						email: tokenDecodificado.email,
						name: tokenDecodificado.name,
						permission: tokenDecodificado.Permission,
						clienteId: '',
						clientes: [],
						clienteNomeFantasia: '',
						clienteCnpj: '',
						clienteBaseDados: '',
						tokenExp: new Date(tokenDecodificado.exp * 1000),
					};

					if (tokenDecodificado.clientes) {
						try {
							const clientesArray = JSON.parse(tokenDecodificado.clientes);
							if (Array.isArray(clientesArray) && clientesArray.length > 0) {
								dadosUsuario.clienteId = clientesArray[0].id;
								dadosUsuario.clientes = clientesArray;
								dadosUsuario.clienteNomeFantasia = clientesArray[0].nome_fantasia;
								dadosUsuario.clienteCnpj = clientesArray[0].cnpj;
								dadosUsuario.clienteBaseDados = clientesArray[0].base_dados;
							}
						} catch (e) {
							console.error('Erro ao parsear clientes do token:', e);
						}
					}

					// Inicia a sessao com 1 minuto de duração somente para testes
					// const agora = new Date();
					// const horaFimSessao = new Date(agora.getTime() + 1 * 60 * 1000); 
					//FIM - Inicia a sessao com 1 minuto de duração somente para testes

					const horaFimSessao = new Date(tokenDecodificado.exp * 1000);

					sessionStorage.setItem(this.TOKEN_KEY, authResult.result.token);
					sessionStorage.setItem(this.USER_KEY, JSON.stringify(dadosUsuario));
					sessionStorage.setItem(this.SESSION_EXPIRY_KEY, horaFimSessao.toISOString());

					this.usuarioAtualSubject.next(dadosUsuario);
				}
			} catch (error) {
				const horaFimSessao = new Date(Date.now() + this.SESSION_DURATION);
				sessionStorage.setItem(this.TOKEN_KEY, authResult.result.token);
				sessionStorage.setItem(this.SESSION_EXPIRY_KEY, horaFimSessao.toISOString());
			}
			this.estaAutenticadoSubject.next(true);
		}
	}

	private limparSessao(): void {
		if (this.navegadorAtivo) {
			sessionStorage.removeItem(this.TOKEN_KEY);
			sessionStorage.removeItem(this.USER_KEY);
			sessionStorage.removeItem(this.SESSION_EXPIRY_KEY);
		}
		this.usuarioAtualSubject.next(null);
		this.estaAutenticadoSubject.next(false);
		this.stopSessionCheck();
		this.router.navigate(['/login']);
	}

	private carregaDadosDaSessao(): void {
		if (this.navegadorAtivo) {
			if (!this.sessaoValida()) {
				this.limparDadosCorrompidos();
				return;
			}

			const userStr = sessionStorage.getItem(this.USER_KEY);
			const token = sessionStorage.getItem(this.TOKEN_KEY);

			if (userStr && userStr !== 'undefined' && userStr !== 'null' && token) {
				try {
					const user = JSON.parse(userStr);
					const tokenDecodificado = this.decodificarToken(token);
					if (tokenDecodificado) {
						const tokenExpiry = new Date(tokenDecodificado.exp * 1000);

						const agora = new Date();
						// const tokenExpiry = new Date(agora.getTime() + 1 * 60 * 1000); // Descomentar somente para testes

						if (agora >= tokenExpiry) {
							this.limparDadosCorrompidos();
							return;
						}
						sessionStorage.setItem(this.SESSION_EXPIRY_KEY, tokenExpiry.toISOString());
					}
					this.usuarioAtualSubject.next(user);
					this.estaAutenticadoSubject.next(true);
					this.restaurarFluxoSessao();
				} catch (error) {
					console.error('Error sessionStorage:', error);
					this.limparDadosCorrompidos();
				}
			}
		}
	}

	public restaurarFluxoSessao(): void {
		if (this.navegadorAtivo && this.estaAutenticadoSubject.value) {
			this.iniciaValidadeSessao();
		}
	}

	limparDadosCorrompidos(): void {
		if (this.navegadorAtivo) {
			sessionStorage.removeItem(this.TOKEN_KEY);
			sessionStorage.removeItem(this.USER_KEY);
			sessionStorage.removeItem(this.SESSION_EXPIRY_KEY);
		}
		this.usuarioAtualSubject.next(null);
		this.estaAutenticadoSubject.next(false);
	}

	getToken(): string | null {
		if (!this.navegadorAtivo) return null;

		if (!this.sessaoValida()) {
			this.limparDadosCorrompidos();
			return null;
		}

		return sessionStorage.getItem(this.TOKEN_KEY);
	}

	getRefreshToken(): string | null {
		if (!this.navegadorAtivo) return null;

		if (!this.sessaoValida()) {
			this.limparDadosCorrompidos();
			return null;
		}

		const userStr = sessionStorage.getItem(this.USER_KEY);
		if (!userStr) return null;

		try {
			const dadosUsuarioLogado: DadosUsuario = JSON.parse(userStr);
			return dadosUsuarioLogado.refreshToken;
		} catch (error) {
			console.error('Error parsing user data:', error);
			return null;
		}
	}

	private hasToken(): boolean {
		return !!this.getToken();
	}

	getUsuarioAtual(): DadosUsuario | null {
		return this.usuarioAtualSubject.value;
	}


	private decodificarToken(token: string): DadosJwt | null {
		try {
			return jwtDecode<DadosJwt>(token);
		} catch (error) {
			console.error('Error decoding token:', error);
			return null;
		}
	}

	private handleError(error: any) {
		if (error.error && typeof error.error === 'string' && error.error.includes('<!DOCTYPE')) {
			console.error('Recebido HTML ao inves de JSON - API pode estar indisponível');
			return throwError(() => ({
				error: { message: 'Erro de conexão com o servidor. API indisponível.' },
			}));
		}

		if (error.status === 0) {
			console.error('CORS error or network issue');
			return throwError(() => ({
				error: {
					message: 'Erro de CORS. Verifique se a API permite requisições do seu domínio.',
				},
			}));
		}

		return throwError(() => error);
	}

	private sessaoValida(): boolean {
		if (!this.navegadorAtivo) return false;

		const token = sessionStorage.getItem(this.TOKEN_KEY);
		const horaFimSessao = sessionStorage.getItem(this.SESSION_EXPIRY_KEY);

		if (!token || !horaFimSessao) return false;

		const horaFim = new Date(horaFimSessao).getTime();
		const now = new Date().getTime();

		return now < horaFim;
	}

	private readonly TEMPO_INICIO_CONTAGEM = 1 * 60 * 1000;

	public iniciaValidadeSessao(): void {
		if (!this.navegadorAtivo) return;

		const startSessionValidation = () => {
			if (this.sessionCheckInterval) return;

			this.sessionCheckInterval = setInterval(() => {
				if (!this.sessaoValida() && this.estaAutenticadoSubject.value) {
					this.forcaLogout();
				}
			}, 3000);
		};

		const checkAndStart = () => {
			const timeRemaining = this.getSessionTimeRemaining();

			if (timeRemaining <= this.TEMPO_INICIO_CONTAGEM) {
				startSessionValidation();
			} else {
				setTimeout(checkAndStart, timeRemaining - this.TEMPO_INICIO_CONTAGEM);
			}
		};

		checkAndStart();
	}

	private stopSessionCheck(): void {

		if (this.sessionCheckInterval) {
			clearInterval(this.sessionCheckInterval);
			this.sessionCheckInterval = null;
		}
	}

	private forcaLogout(): void {

		let dadosNotificacao: DadosDialogoNotificacao = {
			titulo: 'Sessão Expirada!',
			descricao: 'Sua sessão expirou por motivos de segurança. Deseja permanecer acessando?',
			corHeader: 'info',
			mostrarBotaoCancelar: true,
		};

		this.dialogoSerivce.dialogoNotificacao(dadosNotificacao).subscribe({
			next: (retorno) => {
				if (retorno.confirmado) {
					const dadosAtualizarToken: refrashTokenRequest = {
						refreshToken: this.getUsuarioAtual()?.refreshToken || '',
					};

					this.atualizarToken(dadosAtualizarToken).subscribe({
						error: (error) => {
							console.error('Erro ao atualizar token:', error);
							this.limparSessao();
							this.dialogoSerivce.fecharTodosModais();
						},
					});
				} else {
					this.limparSessao();
					this.dialogoSerivce.fecharTodosModais();
				}
			}

		});

		this.stopSessionCheck()
	}

	getSessionTimeRemaining(): number {

		if (!this.navegadorAtivo) return 0;

		const horaFimSessao = sessionStorage.getItem(this.SESSION_EXPIRY_KEY);
		if (!horaFimSessao) return 0;

		const expiryDate = new Date(horaFimSessao);
		const now = new Date();

		return Math.max(0, expiryDate.getTime() - now.getTime());
	}

	getSessionTimeRemainingFormatted(): string {
		const timeRemaining = this.getSessionTimeRemaining();
		if (timeRemaining <= 0) return '00:00:00';

		const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
		const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
		const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	}
}
