export interface RegraCondicional {
    Id: string;
    IdConfiguracao: string;
    nome: string;
    valor: string;
    tipagem: string;
    operador: number;
    ativo: boolean;
}

export interface ConfiguracaoProcessamento {
    Id: string;
    nome: string;
    tipoProcessamento: number;
    tipoConfiguracao: number;
    ativo: boolean;
    criadoEm?: string;
    atualizadoEm?: string;
    criadoPor?: string; 
    alteradoPor?: string; 
    regras: RegraCondicional[];
}