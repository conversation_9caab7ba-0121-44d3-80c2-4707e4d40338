import { TipoDialogoDescList } from "../enums/dialogos/tipo-dialogo-descricao-list.enum";

export interface ConfiguracaoDialogoLista {
  tipo:  TipoDialogoDescList;
  icone?: string;
  corIcone?: string;
  estiloItem?: string;
  estiloContainer?: string;
}

export class DialogoDescricaoListaUtil {
  
  public static readonly CONFIGURACOES_PADRAO: Record<string, ConfiguracaoDialogoLista> = {
    condicoes: {
      tipo: TipoDialogoDescList.condicoes,
      icone: 'feather icon-alert-triangle',
      corIcone: 'text-warning',
      estiloItem: 'border: solid 1px #ededed; padding: 0.3rem; padding-left: 1rem; margin-top: -1px;',
      estiloContainer: 'padding-left: 0.5rem; margin-bottom: 0px;'
    },
    divergencias: {
      tipo: TipoDialogoDescList.divergencias,
      icone: 'feather icon-alert-triangle',
      corIcone: 'text-danger',
      estiloItem: 'border: solid 1px #ededed; padding: 0.3rem; padding-left: 1rem; margin-top: -1px;',
      estiloContainer: 'padding-left: 0.5rem; margin-bottom: 0px;'
    },
  };

   public static obterConfiguracaoPadrao(tipo: TipoDialogoDescList): ConfiguracaoDialogoLista | null {
    return this.CONFIGURACOES_PADRAO[tipo] || null;
  }

  public static obterTituloPadrao(
    tipo: TipoDialogoDescList
  ): string {
    const titulos = {
      [TipoDialogoDescList.condicoes]: 'Condições',
      [TipoDialogoDescList.divergencias]: 'Divergências',
    };

    return titulos[tipo] || 'Lista';
  }

  public static obterCorIcone(
    tipo: TipoDialogoDescList
  ): string {
    return this.CONFIGURACOES_PADRAO[tipo]?.corIcone || 'text-primary';
  }
}
