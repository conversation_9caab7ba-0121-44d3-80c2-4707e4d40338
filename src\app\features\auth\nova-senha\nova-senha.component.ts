import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../core/auth/services/auth.service';
import { CadastroNovoUsuarioRequest, novaSenhaRequest } from '../../../shared/interfaces/auth/requests';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';

@Component({
	selector: 'app-nova-senha',
	imports: [CommonModule, ReactiveFormsModule],
	templateUrl: './nova-senha.component.html',
	styleUrl: './nova-senha.component.scss',
})
export class NovaSenhaComponent {
	formulario: FormGroup;
	email: string = '';
	token: string = '';
	enviado: boolean = false;
	hideNewPassword = true;
	hideConfirmPassword = true;
	isLoading = false;
	errorMessage = '';

	private fb = inject(FormBuilder);
	private route = inject(ActivatedRoute);
	private router = inject(Router);
	private authService = inject(AuthService);
	private dialogoService = inject(DialogoService);

	constructor() {
		this.formulario = this.fb.group(
			{
				newPassword: ['',
                    [Validators.required, Validators.minLength(6), Validators.pattern(/^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).+$/)],
				],
				confirmPassword: ['', Validators.required],
			},
			{ validators: this.senhasIguaisValidator }
		);
	}

	senhasIguaisValidator: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
		const senha = group.get('newPassword')?.value;
		const confirma = group.get('confirmPassword')?.value;
		if (senha && confirma && senha !== confirma) {
			return { senhasDiferentes: true };
		}
		return null;
	};

	ngOnInit(): void {
		this.email = this.route.snapshot.queryParamMap.get('email') || '';
		const tokenEncoded = this.route.snapshot.queryParamMap.get('token') || '';
		this.token = decodeURIComponent(tokenEncoded);
        
	}

	enviar(): void {
		if (this.formulario.valid) {
			const dadosUsuario: novaSenhaRequest = {
				email: this.email,
				resetCode: this.token,
				newPassword: this.formulario.value.newPassword,
			};

			this.authService.novaSenha(dadosUsuario).subscribe({
				next: (response) => {
					this.enviado = true;
                    this.dialogoService.dialogoNotificacao({
						titulo: 'Redifinição de Senha',
						descricao: 'Senha redefinida com sucesso. Faça login novamente.',
						corHeader: 'success',
						mostrarBotaoCancelar: false,
						botaoConfirmar: {
							texto: 'OK',
						},
					}).subscribe(() => {
						this.router.navigate(['/auth/login']);
					});
					
				},
				error: (error) => {
					this.enviado = true;
					console.error('Erro ao cadastrar nova senha:', error);
					this.dialogoService
						.dialogoNotificacao({
							titulo: 'Erro ao cadastrar nova senha',
							descricao: error.error.result.message || 'Ocorreu um erro ao cadastrar a nova senha.',
							corHeader: 'danger',
							mostrarBotaoCancelar: false,
							botaoConfirmar: {
								texto: 'OK',
								classe: 'btn-danger',
							},
						})
						.subscribe();
				},
			});
		}
	}
}
