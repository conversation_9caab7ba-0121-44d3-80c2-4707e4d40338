import { Component, inject, OnInit, ChangeDetectorRef, ElementRef, AfterViewInit } from '@angular/core';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormBuilder, FormGroup, FormsModule, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ColDef } from 'ag-grid-community';
import { TabelaPadraoComponent } from '../../../shared/components/tabelas/tabela-padrao/tabela-padrao.component';
import { PessoaService } from '../../../core/services/api/pessoa.service';
import { EstadoService } from '../../../core/services/api/estado.service';
import { Title } from '@angular/platform-browser';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PessoaEndereco } from '../../../shared/interfaces/api/pessoa-endereco.interface';
import { Pessoa } from '../../../shared/interfaces/api/pessoa.interface';
import { ViewChild } from '@angular/core';
import { SelectSimplesComponent } from '../../../shared/components/selects/select-simples/select-simples.component';
import { CidadeService } from '../../../core/services/api/cidade.service';
import { EnderecoService } from '../../../core/services/api/endereco.service';
import { PessoaContato } from '../../../shared/interfaces/api/pessoa-contato.interface';
import { ContatoService } from '../../../core/services/api/contato.service';
import { BancoService } from '../../../core/services/api/banco.service';
import { PessoaDadoBancario } from '../../../shared/interfaces/api/pessoa-dado-bancario.interface';
import { DadoBancarioService } from '../../../core/services/api/dadoBancario.service';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
	selector: 'app-pessoa-juridica-cadastro',
	imports: [
		CommonModule,
		FormsModule,
		NgMultiSelectDropDownModule,
		RouterModule,
		CardComponent,
		TabelaPadraoComponent,
		NgbModule,
		SelectSimplesComponent,
		ReactiveFormsModule,
		NgxMaskDirective,
	],
	templateUrl: './pessoa-juridica-cadastro.component.html',
	styleUrl: './pessoa-juridica-cadastro.component.scss',
})
export class PessoaJuridicaCadastroComponent implements OnInit, AfterViewInit {
	private dialogoService = inject(DialogoService);
	private pessoaService = inject(PessoaService);
	private estadoService = inject(EstadoService);
	private cidadeService = inject(CidadeService);
	private enderecoService = inject(EnderecoService);
	private contatoService = inject(ContatoService);
	private dadoBancarioService = inject(DadoBancarioService);
	private bancoService = inject(BancoService);
	private formBuilder = inject(FormBuilder);

	@ViewChild('inputRazaoSocial') inputRazaoSocial!: ElementRef;
	@ViewChild('inputCep') inputCep!: ElementRef;
	@ViewChild('inputContatoNome') inputContatoNome!: ElementRef;
	@ViewChild('inputBanco') inputBanco!: ElementRef;

	dadosGeraisForm: FormGroup;
	enderecosForm: FormGroup;
	contatosForm: FormGroup;
	dadosBancariosForm: FormGroup;

	edicaoEnderecoFechada = true;
	mostrarBotaoIncluirEndereco = true;

	edicaoContatoFechada = true;
	mostrarBotaoIncluirContato = true;

	edicaoDadosBancariosFechada = true;
	mostrarBotaoIncluirDadosBancarios = true;

	logradouro: string = '';
	cep: string = '';
	numero: number = 0;
	complemento: string = '';
	bairro: string = '';
	estado: number = 0;
	cidade: number = 0;

	banco: number = 0;
	agencia: string = '';
	agenciaDV?: string = '';
	conta: string = '';
	contaDV: string = '';
	tipoConta?: number;
	titular: string = '';
	cpfCnpj: string = '';
	pix: string | undefined = '';
	bancos: { valor: number; nome: string }[] = [];
	tiposConta: { valor: number; nome: string }[] = [];

	contatoNome: string = '';
	contatoEmail: string = '';
	contatoTipos: { item_id: number; item_text: string }[] = [];
	contatoTipos_selecionados: { item_id: number; item_text: string }[] = [];
	contatoTipos_Todos: { valor: string; nome: string }[] = [];
	contatoTipoTelefone_Todos: { valor: string; nome: string }[] = [];
	contatoAtivo: boolean = true;

	enderecoEditando: PessoaEndereco | null = null;
	contatoEditando: PessoaContato | null = null;
	dadoBancarioEditando: PessoaDadoBancario | null = null;

	public ColunasGridEnderecos: ColDef[] = [
		{ field: 'logradouro', headerName: 'Logradouro', flex: 0.3 },
		{ field: 'numero', headerName: 'Número', flex: 0.1 },
		{ field: 'bairro', headerName: 'Bairro', flex: 0.2 },
		{
			field: 'cep',
			headerName: 'CEP',
			flex: 0.2,
			valueFormatter: (params: any) => {
				const cep = params.value?.toString() ?? '';
				return cep.length === 8 ? `${cep.slice(0, 5)}-${cep.slice(5)}` : cep;
			},
		},
		{
			headerName: 'Principal',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';

				if (params.data.principal) {
					button.className = 'feather icon-star text-primary';
				} else {
					button.className = 'feather icon-star text-white';
				}
				button.addEventListener('click', (e) => {
					e.stopPropagation();
					this.alterarEnderecoPrincipal(params.data.id);
				});

				return button;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Ações',
			cellRenderer: (params: any) => {
				const editButton = document.createElement('span');
				editButton.title = 'editar';
				editButton.style.cursor = 'pointer';
				editButton.style.fontSize = '20px';
				editButton.style.color = '#818181ff';
				editButton.className = 'feather icon-edit text-primary me-2';
				editButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.editarEndereco(params.data.id);
				});

				const deleteButton = document.createElement('span');
				deleteButton.title = 'excluir';
				deleteButton.style.cursor = 'pointer';
				deleteButton.style.fontSize = '20px';
				deleteButton.style.color = '#dc3545';
				deleteButton.className = 'feather icon-trash-2 text-danger';
				deleteButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.excluirEndereco(params.data.id);
				});

				const container = document.createElement('div');
				container.appendChild(editButton);
				container.appendChild(deleteButton);

				return container;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
	];

	public ColunasGridContatos: ColDef[] = [
		{ field: 'nome', headerName: 'Nome', flex: 0.8 },
		{ field: 'email', headerName: 'E-Mail', flex: 0.8 },
		{
			headerName: 'Ativo',
			cellRenderer: (params: any) => {
				const editButton = document.createElement('span');
				editButton.style.cursor = 'default';
				editButton.style.fontSize = '20px';
				editButton.style.color = '#818181ff';
				if (params.data.ativo) {
					editButton.className = 'feather icon-check text-info';
					editButton.title = 'Sim';
				} else {
					editButton.title = 'Não';
				}
				const container = document.createElement('div');
				container.appendChild(editButton);
				return container;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Ações',
			cellRenderer: (params: any) => {
				const editButton = document.createElement('span');
				editButton.title = 'editar';
				editButton.style.cursor = 'pointer';
				editButton.style.fontSize = '20px';
				editButton.style.color = '#818181ff';
				editButton.className = 'feather icon-edit text-primary me-2';
				editButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.editarContato(params.data.id);
				});
				const container = document.createElement('div');
				container.appendChild(editButton);
				return container;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
	];

	public ColunasGridDadosBancarios: ColDef[] = [
		{ field: 'nomeBanco', headerName: 'Banco', flex: 0.2 },
		{ field: 'agencia', headerName: 'Agência', flex: 0.1 },
		{ field: 'conta', headerName: 'Conta', flex: 0.2 },
		{ field: 'titular', headerName: 'Titular', flex: 0.3 },
		{
			headerName: 'Principal',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';

				if (params.data.principal) {
					button.className = 'feather icon-star text-primary';
				} else {
					button.className = 'feather icon-star text-white';
				}
				button.addEventListener('click', (e) => {
					e.stopPropagation();
					this.alterarDadoBancarioPrincipal(params.data.id_dados_bancarios);
				});

				return button;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Ações',
			cellRenderer: (params: any) => {
				const editButton = document.createElement('span');
				editButton.title = 'editar';
				editButton.style.cursor = 'pointer';
				editButton.style.fontSize = '20px';
				editButton.style.color = '#818181ff';
				editButton.className = 'feather icon-edit text-primary me-2';
				editButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.editarDadoBancario(params.data.id_dados_bancarios);
				});

				const deleteButton = document.createElement('span');
				deleteButton.title = 'excluir';
				deleteButton.style.cursor = 'pointer';
				deleteButton.style.fontSize = '20px';
				deleteButton.style.color = '#dc3545';
				deleteButton.className = 'feather icon-trash-2 text-danger';
				deleteButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.excluirDadoBancario(params.data.id_dados_bancarios);
				});

				const container = document.createElement('div');
				container.appendChild(editButton);
				container.appendChild(deleteButton);

				return container;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
	];

	UFs: { valor: any; nome: any }[] = [];
	Cidades: { valor: any; nome: any }[] = [];

	contatoTipoSettings = {
		singleSelection: false,
		idField: 'item_id',
		textField: 'item_text',
		selectAllText: 'Selecionar todos',
		unSelectAllText: 'Remover todos',
		itemsShowLimit: 3,
		allowSearchFilter: true,
	};

	activeTab = 1;

	pessoa: Pessoa = this.novaPessoa();

	constructor(
		private titleService: Title,
		private cdr: ChangeDetectorRef,
		private route: ActivatedRoute
	) {
		this.dadosGeraisForm = this.formBuilder.group({
			razaoSocial: ['', [Validators.required, Validators.minLength(3)]],
			nomeFantasia: ['', [Validators.required, Validators.minLength(3)]],
			cnpj: ['', [Validators.required, Validators.minLength(14), Validators.maxLength(14)]],
		});
		this.enderecosForm = this.formBuilder.group({
			cep: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(8)]],
			logradouro: ['', [Validators.required, Validators.minLength(3)]],
			numero: ['', [Validators.required, Validators.minLength(1)]],
			complemento: ['', []],
			bairro: ['', [Validators.required, Validators.minLength(3)]],
		});
		this.contatosForm = this.formBuilder.group({
			nome: ['', [Validators.required, Validators.minLength(3)]],
			email: ['', [Validators.required, Validators.email]],
			ativo: [true],
			contatoTipo: [null, [Validators.required]],
			telefones: this.formBuilder.array([this.criarTelefone()]),
		});
		this.dadosBancariosForm = this.formBuilder.group({
			banco: [''],
			agencia: [''],
			agenciaDV: ['', [Validators.required]],
			conta: ['', [Validators.required]],
			contaDV: ['', [Validators.required]],
			titular: ['', [Validators.required]],
			cpfCnpj: ['', [Validators.required]],
			pix: [''],
		});
	}

	ngOnInit() {
		this.titleService.setTitle('Pessoa Jurídica | ZinPag');
		this.route.queryParamMap.subscribe((query: any) => {
			const params = query['params'];
			const id = params['id'];
			if (id) {
				this.pessoaService.getById(id).subscribe((p) => {
					this.pessoa = p;
					this.pessoa.enderecos = [...(this.pessoa.enderecos ?? [])];
					this.pessoa.contatos = [...(this.pessoa.contatos ?? [])];
					this.pessoa.dadosBancarios = [...(this.pessoa.dadosBancarios ?? [])];
					this.cdr.detectChanges();
				});
			}
		});
		this.ObterEstados();
		this.ObterTiposContato();
		this.ObterTiposTelefones();
		this.ObterBancos();
		this.ObterTiposConta();
	}

	ngAfterViewInit() {
		if (this.inputRazaoSocial) {
			this.inputRazaoSocial.nativeElement.focus();
			window.scrollTo(0, 0);
		}
	}

	goToProximaAba() {
		if (this.dadosGeraisForm.invalid) {
			this.dadosGeraisForm.markAllAsTouched();
			this.exibirMensagem('Preencha todos os campos obrigatórios da aba Dados Gerais.');
			return;
		}
		if (this.activeTab < 4) this.activeTab++;
	}

	goToAbaAnterior() {
		if (this.activeTab > 1) this.activeTab--;
	}

	ObterEstados() {
		this.estadoService.listar().subscribe({
			next: (estados) => {
				this.UFs = estados.map((estado: any) => ({
					valor: estado.id,
					nome: estado.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	ObterTiposContato() {
		this.contatoService.tipos().subscribe({
			next: (tipos) => {
				this.contatoTipos_Todos = tipos.map((tipo: any) => ({
					valor: tipo.id,
					nome: tipo.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	ObterTiposTelefones() {
		this.contatoService.tiposTelefones().subscribe({
			next: (tipos) => {
				this.contatoTipoTelefone_Todos = tipos.map((tipo: any) => ({
					valor: tipo.id,
					nome: tipo.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	ObterBancos() {
		this.bancoService.listar().subscribe({
			next: (bancos) => {
				this.bancos = bancos.map((banco: any) => ({
					valor: banco.id,
					nome: banco.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	ObterTiposConta() {
		this.bancoService.tiposConta().subscribe({
			next: (tipos) => {
				this.tiposConta = tipos.map((tipo: any) => ({
					valor: tipo.id,
					nome: tipo.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	onEstadoChange(value: any) {
		this.estado = value;
		this.cidadeService.listar(value).subscribe({
			next: (cidades) => {
				this.Cidades = cidades.map((cidade: any) => ({
					valor: cidade.id,
					nome: cidade.nome,
				}));
				this.cdr.detectChanges();
			},
		});
	}

	onCidadeChange(value: any) {
		this.cidade = value;
	}

	onTipoContatoChange(event: any): void {
		this.contatoTipos = event;
	}

	onBancoChange(value: any) {
		this.banco = value;
	}

	onTipoContaChange(value: any) {
		this.tipoConta = value;
	}

	novaPessoa(): Pessoa {
		return { TipoPessoa: 200, enderecos: [] };
	}

	novoEndereco() {
		this.logradouro = '';
		this.cep = '';
		this.numero = 0;
		this.complemento = '';
		this.bairro = '';
		this.estado = 0;
		this.cidade = 0;
		this.exibirEdicaoEndereco(true);
		setTimeout(() => {
			this.inputCep.nativeElement.focus();
		}, 200);
	}

	editarEndereco(id: number) {
		this.enderecoEditando = this.pessoa.enderecos?.find((e) => e.id === id) ?? null;
		if (this.enderecoEditando) {
			this.cep = this.enderecoEditando.cep;
			this.logradouro = this.enderecoEditando.logradouro;
			this.numero = this.enderecoEditando.numero;
			this.complemento = this.enderecoEditando.complemento;
			this.bairro = this.enderecoEditando.bairro;
			this.onEstadoChange(this.enderecoEditando.estado);
			this.cidade = this.enderecoEditando.cidade;
			this.exibirEdicaoEndereco(true);
			this.cdr.detectChanges();
		}
	}

	salvarEndereco() {
		if (this.enderecosForm.invalid) {
			this.enderecosForm.markAllAsTouched();
			this.exibirMensagem('Preencha todos os campos obrigatórios corretamente.');
			return;
		}

		if (this.enderecoEditando) {
			this.enderecoEditando.cep = this.cep;
			this.enderecoEditando.logradouro = this.logradouro;
			this.enderecoEditando.numero = this.numero;
			this.enderecoEditando.complemento = this.complemento;
			this.enderecoEditando.bairro = this.bairro;
			this.enderecoEditando.estado = this.estado;
			this.enderecoEditando.cidade = this.cidade;
			if (this.pessoa.id && typeof this.enderecoEditando.id === 'number') {
				this.enderecoService.atualizar(this.enderecoEditando.id, this.enderecoEditando).subscribe({
					next: (response) => {
						this.exibirMensagem('Endereço salvo com sucesso.');
						this.cdr.detectChanges();
					},
					error: (error) => {
						this.exibirMensagem('Erro ao salvar endereço.');
					},
				});
			}
			this.enderecoEditando = null;
		} else {
			const novoEndereco: PessoaEndereco = {
				id: (this.pessoa.enderecos?.length ?? 0) + 1,
				cep: this.cep,
				logradouro: this.logradouro,
				complemento: this.complemento,
				numero: this.numero,
				bairro: this.bairro,
				ativo: true,
				principal: true,
				estado: this.estado,
				cidade: this.cidade,
				pessoa: this.pessoa.id ?? 0,
			};

			if (this.pessoa.id) {
				this.pessoaService.incluirEndereco(novoEndereco).subscribe({
					next: (response) => {
						novoEndereco.id = Number(response);
						this.pessoa.enderecos = this.pessoa.enderecos ?? [];
						this.pessoa.enderecos.push(novoEndereco);
						this.pessoa.enderecos = [...(this.pessoa.enderecos ?? [])];
						this.cdr.detectChanges();
						this.exibirMensagem('Endereço incluído com sucesso.');
					},
					error: () => {
						this.exibirMensagem('Erro ao incluir endereço.');
					},
				});
			} else {
				this.pessoa.enderecos = this.pessoa.enderecos ?? [];
				this.pessoa.enderecos.push(novoEndereco);
			}
		}

		this.pessoa.enderecos = [...(this.pessoa.enderecos ?? [])];
		this.exibirEdicaoEndereco(false);
	}

	excluirEndereco(id: number) {
		this.dialogoService
			.dialogoNotificacao({
				titulo: 'ZinPag',
				descricao: 'Confirma exclusão do endereço?',
				mostrarBotaoCancelar: true,
				botaoConfirmar: {
					texto: 'OK',
					classe: 'btn-primary',
					icone: 'fa fa-check',
					habilitaIcone: true,
				},
				botaoCancelar: {
					texto: 'Cancelar',
					classe: 'btn-outline-primary',
					icone: 'fa fa-times',
					habilitaIcone: true,
				},
			})
			.subscribe((result) => {
				if (result.confirmado) {
					//Exclui do banco.
					if (this.pessoa.id) {
						this.enderecoService.deletar(id).subscribe({
							next: (response) => {
								this.exibirMensagem('Endereço excluído com sucesso.');
							},
							error: (error) => {
								this.exibirMensagem('Erro ao excluir endereço.');
							},
						});
					}
					//Remove do array.
					const endereco = this.pessoa.enderecos?.find((e) => e.id === id) ?? null;
					if (endereco) {
						const index = this.pessoa.enderecos?.findIndex((e) => e.id === id);
						if (index !== undefined && index > -1 && this.pessoa.enderecos) this.pessoa.enderecos.splice(index, 1);
						this.pessoa.enderecos = [...(this.pessoa.enderecos ?? [])];
						this.cdr.detectChanges();
					}
				}
			});
	}

	alterarEnderecoPrincipal(id: number) {
		//Atualiza no banco.
		if (this.pessoa.id) {
			this.pessoaService.atualizarEnderecoPrincipal(this.pessoa.id, id).subscribe({
				next: () => {
					this.exibirMensagem('Endereço principal atualizado com sucesso.');
				},
				error: () => {
					this.exibirMensagem('Erro ao atualizar endereço principal.');
				},
			});
		}
		const enderecos = this.pessoa.enderecos ?? [];
		enderecos.forEach((e) => (e.principal = false)); // Todos como false
		const endereco = enderecos.find((e) => e.id === id);
		if (endereco) {
			endereco.principal = true; // Apenas o selecionado como true
			this.pessoa.enderecos = [...enderecos];
			this.cdr.detectChanges();
		}
	}

	exibirEdicaoEndereco(exibir: boolean) {
		this.edicaoEnderecoFechada = !exibir;
		this.mostrarBotaoIncluirEndereco = !exibir;
	}

	popularContatoTipos() {
		this.contatoTipos = this.contatoTipos_Todos.map((tipo) => ({
			item_id: Number(tipo.valor),
			item_text: tipo.nome,
		}));
	}

	novoContato() {
		this.popularContatoTipos();
		this.contatoNome = '';
		this.contatoEmail = '';
		this.contatoTipos_selecionados = [];
		this.contatoAtivo = true;
		this.exibirEdicaoContato(true);

		this.contatosForm.setControl('telefones', this.formBuilder.array([this.criarTelefone()]));

		setTimeout(() => {
			this.inputContatoNome.nativeElement.focus();
		}, 200);
	}

	cancelarEdicaoContato() {
		this.contatoTipos = [];
		this.cdr.detectChanges();
		this.exibirEdicaoContato(false);
	}

	editarContato(id: number) {
		this.contatoEditando = this.pessoa.contatos?.find((e) => e.id === id) ?? null;
		if (this.contatoEditando) {
			this.popularContatoTipos();
			this.contatoNome = this.contatoEditando.nome;
			this.contatoEmail = this.contatoEditando.email;
			this.cdr.detectChanges();
			this.contatoAtivo = this.contatoEditando.ativo;

			if (this.contatoEditando.tipos && Array.isArray(this.contatoEditando.tipos)) {
				this.contatoTipos_selecionados = [];
				for (const tipoId of this.contatoEditando.tipos) {
					const tipo = this.contatoTipos.find((x) => Number(x.item_id) == Number(tipoId));
					if (tipo) {
						this.contatoTipos_selecionados.push(tipo);
					}
				}
				this.cdr.detectChanges();
			}

			if (this.contatoEditando && Array.isArray(this.contatoEditando.telefones)) {
				const telefonesFormArray = this.formBuilder.array(
					this.contatoEditando.telefones.map((tel) =>
						this.formBuilder.group({
							id_contato_telefone: [tel.id_contato_telefone ?? 0],
							telefone: [tel.numero, Validators.required],
							ramal: [tel.ramal ?? '', Validators.required],
							tipo: [tel.tipo, Validators.required],
						})
					)
				);
				telefonesFormArray.controls.forEach((grupo) => grupo.disable());
				this.contatosForm.setControl('telefones', telefonesFormArray);
			}

			this.exibirEdicaoContato(true);
			this.cdr.detectChanges();
		}
	}

	salvarContato() {
		if (this.contatosForm.invalid) {
			this.contatosForm.markAllAsTouched();
			this.exibirMensagem('Preencha todos os campos obrigatórios corretamente.');
			return;
		}

		if (this.contatoEditando) {
			this.contatoEditando.nome = this.contatoNome;
			this.contatoEditando.email = this.contatoEmail;
			this.contatoEditando.tipos = this.contatoTipos_selecionados.map((item) => item.item_id);
			this.contatoEditando.telefones = this.telefones
				.getRawValue()
				.map((t: { id_contato_telefone: number; telefone: string; ramal: string; tipo: string }) => ({
					id_contato_telefone: t.id_contato_telefone,
					numero: Number(t.telefone),
					ramal: Number(t.ramal),
					tipo: Number(t.tipo),
				}));
			this.contatoEditando.ativo = this.contatoAtivo;
			if (this.pessoa.id) {
				this.contatoService.atualizar(this.contatoEditando.id, this.contatoEditando).subscribe({
					next: () => {
						this.exibirMensagem('Contato salvo com sucesso.');
						this.cdr.detectChanges();
					},
					error: () => {
						this.exibirMensagem('Erro ao salvar contato.');
					},
				});
			}
			this.contatoEditando = null;
		} else {
			const novoContato: PessoaContato = {
				id: (this.pessoa.contatos?.length ?? 0) + 1,
				nome: this.contatoNome,
				email: this.contatoEmail,
				idPessoa: this.pessoa.id ?? 0,
				ativo: this.contatoAtivo,
				tipos: this.contatoTipos_selecionados.map((item) => item.item_id),
				telefones: this.telefones.value.map((t: { telefone: string; ramal: string; tipo: string }) => ({
					numero: t.telefone,
					ramal: t.ramal,
					tipo: t.tipo,
				})),
			};

			if (this.pessoa.id) {
				this.pessoaService.incluirContato(novoContato).subscribe({
					next: (response) => {
						novoContato.id = Number(response);
						this.pessoa.contatos = this.pessoa.contatos ?? [];
						this.pessoa.contatos.push(novoContato);
						this.pessoa.contatos = [...(this.pessoa.contatos ?? [])];
						this.cdr.detectChanges();
						this.exibirMensagem('Contato incluído com sucesso.');
					},
					error: (error) => {
						this.exibirMensagem('Erro ao incluir contato.');
					},
				});
			} else {
				this.pessoa.contatos = this.pessoa.contatos ?? [];
				this.pessoa.contatos.push(novoContato);
			}
		}

		this.contatoTipos = [];
		this.pessoa.contatos = [...(this.pessoa.contatos ?? [])];
		this.exibirEdicaoContato(false);
	}

	excluirDadoBancario(id: number) {
		this.dialogoService
			.dialogoNotificacao({
				titulo: 'ZinPag',
				descricao: 'Confirma exclusão do dado bancário?',
				mostrarBotaoCancelar: true,
				botaoConfirmar: {
					texto: 'OK',
					classe: 'btn-primary',
					icone: 'fa fa-check',
					habilitaIcone: true,
				},
				botaoCancelar: {
					texto: 'Cancelar',
					classe: 'btn-outline-primary',
					icone: 'fa fa-times',
					habilitaIcone: true,
				},
			})
			.subscribe((result) => {
				if (result.confirmado) {
					//Exclui do banco.
					if (this.pessoa.id) {
						this.dadoBancarioService.deletar(id).subscribe({
							next: (response) => {
								this.exibirMensagem('Dado bancário excluído com sucesso.');
							},
							error: (error) => {
								this.exibirMensagem('Erro ao excluir dado bancário.');
							},
						});
					}
					//Remove do array.
					const dadoBancario = this.pessoa.dadosBancarios?.find((e) => e.id_dados_bancarios === id) ?? null;
					if (dadoBancario) {
						const index = this.pessoa.dadosBancarios?.findIndex((e) => e.id_dados_bancarios === id);
						if (index !== undefined && index > -1 && this.pessoa.dadosBancarios) this.pessoa.dadosBancarios.splice(index, 1);
						this.pessoa.dadosBancarios = [...(this.pessoa.dadosBancarios ?? [])];
						this.cdr.detectChanges();
					}
				}
			});
	}

	exibirEdicaoContato(exibir: boolean) {
		this.edicaoContatoFechada = !exibir;
		this.mostrarBotaoIncluirContato = !exibir;
	}

	salvar() {
		if (this.dadosGeraisForm.invalid) {
			this.dadosGeraisForm.markAllAsTouched();
			this.exibirMensagem('Preencha todos os campos obrigatórios corretamente.');
			return;
		}

		if (this.pessoa.id && this.pessoa.id > 0) {
			this.pessoaService.atualizar(this.pessoa.id, this.pessoa).subscribe({
				next: () => {
					this.exibirMensagem('Dados gerais salvos com sucesso.');
				},
				error: () => {},
			});
		} else {
			this.pessoaService.criar(this.pessoa).subscribe({
				next: () => {
					this.activeTab = 1;
					this.cdr.detectChanges();
					this.pessoa = this.novaPessoa();
					this.focoNoCampoRazaoSocial();
					this.exibirMensagem('Pessoa Jurídica criada com sucesso.');
				},
				error: () => {},
			});
		}
	}

	exibirMensagem(mensagem: string) {
		this.dialogoService.dialogoNotificacao({
			titulo: 'ZinPag',
			descricao: mensagem,
			mostrarBotaoCancelar: false,
			botaoConfirmar: {
				texto: 'OK',
				classe: 'btn-primary',
				icone: 'fa fa-check',
				habilitaIcone: true,
			},
		});
	}

	focoNoCampoRazaoSocial() {
		setTimeout(() => {
			if (this.inputRazaoSocial) this.inputRazaoSocial.nativeElement.focus();
		}, 200);
	}

	get telefones(): FormArray {
		return this.contatosForm.get('telefones') as FormArray;
	}

	criarTelefone(): FormGroup {
		return this.formBuilder.group({
			id_contato_telefone: [0],
			telefone: ['', Validators.required],
			tipo: ['', Validators.required],
			ramal: [''],
		});
	}

	adicionarTelefone(): void {
		this.telefones.push(this.criarTelefone());
	}

	removerTelefone(index: number): void {
		this.telefones.removeAt(index);
	}

	novoDadoBancario() {
		this.banco = 0;
		this.agencia = '';
		this.agenciaDV = '';
		this.conta = '';
		this.contaDV = '';
		this.titular = '';
		this.cpfCnpj = '';
		this.pix = '';
		this.tipoConta = undefined;
		this.exibirEdicaoDadoBancario(true);
		setTimeout(() => {
			this.inputBanco.nativeElement.focus();
		}, 200);
	}

	editarDadoBancario(id: number) {
		this.dadoBancarioEditando = this.pessoa.dadosBancarios?.find((e) => e.id_dados_bancarios === id) ?? null;
		if (this.dadoBancarioEditando) {
			this.banco = this.dadoBancarioEditando.idBanco;
			this.agencia = this.dadoBancarioEditando.agencia;
			this.agenciaDV = this.dadoBancarioEditando.agenciaDv;
			this.conta = this.dadoBancarioEditando.conta;
			this.contaDV = this.dadoBancarioEditando.contaDv;
			this.titular = this.dadoBancarioEditando.titular;
			this.cpfCnpj = this.dadoBancarioEditando.cpfCnpjTitular;
			this.pix = this.dadoBancarioEditando.pix;
			this.tipoConta = this.dadoBancarioEditando.tipoConta;
			this.exibirEdicaoDadoBancario(true);
			this.cdr.detectChanges();
		}
	}

	salvarDadoBancario() {
		if (this.dadosBancariosForm.invalid) {
			this.dadosBancariosForm.markAllAsTouched();
			const invalidFields = Object.keys(this.dadosBancariosForm.controls)
				.filter((key) => this.dadosBancariosForm.get(key)?.invalid)
				.map((key) => key)
				.join(', ');
			this.exibirMensagem(`Preencha todos os campos obrigatórios corretamente. Campos inválidos: ${invalidFields}`);
			return;
		}

		if (this.dadoBancarioEditando) {
			this.dadoBancarioEditando.idBanco = this.banco;
			this.dadoBancarioEditando.nomeBanco = this.bancos.find((b) => b.valor == this.banco)?.nome ?? '';
			this.dadoBancarioEditando.agencia = this.agencia ?? 0;
			this.dadoBancarioEditando.agenciaDv = this.agenciaDV;
			this.dadoBancarioEditando.conta = this.conta;
			this.dadoBancarioEditando.contaDv = this.contaDV ?? 0;
			this.dadoBancarioEditando.titular = this.titular;
			this.dadoBancarioEditando.cpfCnpjTitular = this.cpfCnpj;
			this.dadoBancarioEditando.pix = this.pix;
			this.dadoBancarioEditando.tipoConta = this.tipoConta ?? 0;

			//Atualiza no banco.
			if (this.pessoa.id && typeof this.dadoBancarioEditando.id_dados_bancarios === 'number') {
				this.dadoBancarioService.atualizar(this.dadoBancarioEditando.id_dados_bancarios, this.dadoBancarioEditando).subscribe({
					next: () => {
						this.exibirMensagem('Dados bancários salvos com sucesso.');
						this.cdr.detectChanges();
					},
					error: () => {
						this.exibirMensagem('Erro ao salvar dados bancários.');
					},
				});
			}

			this.dadoBancarioEditando = null;
		} else {
			const novoDadoBancario: PessoaDadoBancario = {
				id_dados_bancarios: (this.pessoa.dadosBancarios?.length ?? 0) + 1,
				idBanco: this.banco,
				nomeBanco: this.bancos.find((b) => b.valor == this.banco)?.nome ?? '',
				agencia: this.agencia ?? '',
				agenciaDv: this.agenciaDV ?? '',
				conta: this.conta,
				contaDv: this.contaDV ?? '',
				tipoConta: this.tipoConta ?? 0,
				titular: this.titular,
				cpfCnpjTitular: this.cpfCnpj,
				pix: this.pix,
				principal: false,
			};

			if (this.pessoa.id) {
				// Incluiu no banco.
				novoDadoBancario.idPessoa = this.pessoa.id;
				this.pessoaService.incluirDadoBancario(novoDadoBancario).subscribe({
					next: (response) => {
						novoDadoBancario.id_dados_bancarios = Number(response);
						this.pessoa.dadosBancarios = this.pessoa.dadosBancarios ?? [];
						this.pessoa.dadosBancarios.push(novoDadoBancario);
						this.pessoa.dadosBancarios = [...(this.pessoa.dadosBancarios ?? [])];
						this.cdr.detectChanges();
						this.exibirMensagem('Dados bancários incluídos com sucesso.');
					},
					error: () => {
						this.exibirMensagem('Erro ao incluir dados bancários.');
					},
				});
			} else {
				this.pessoa.dadosBancarios = this.pessoa.dadosBancarios ?? [];
				this.pessoa.dadosBancarios.push(novoDadoBancario);
			}
		}

		this.pessoa.dadosBancarios = [...(this.pessoa.dadosBancarios ?? [])];
		this.exibirEdicaoDadoBancario(false);
	}

	alterarDadoBancarioPrincipal(id: number) {
		//Atualiza no banco.
		if (this.pessoa.id) {
			this.pessoaService.atualizarDadoBancarioPrincipal(this.pessoa.id, id).subscribe({
				next: () => {
					this.exibirMensagem('Dado bancário principal atualizado com sucesso.');
				},
				error: () => {
					this.exibirMensagem('Erro ao atualizar dado bancário principal.');
				},
			});
		}
		const dadosBancarios = this.pessoa.dadosBancarios ?? [];
		dadosBancarios.forEach((e) => (e.principal = false)); // Todos como false
		const dadoBancario = dadosBancarios.find((e) => e.id_dados_bancarios === id);
		if (dadoBancario) {
			dadoBancario.principal = true; // Apenas o selecionado como true
			this.pessoa.dadosBancarios = [...dadosBancarios];
			this.cdr.detectChanges();
		}
	}

	exibirEdicaoDadoBancario(exibir: boolean) {
		this.edicaoDadosBancariosFechada = !exibir;
		this.mostrarBotaoIncluirDadosBancarios = !exibir;
	}
}
