import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../auth/services/auth.service';
import { Movimentacao } from '../../../shared/interfaces/api/movimentacao.interface';
import { MockDataService } from '../../../shared/services/mock-data.service';
import { MovimentacoesDetalhes } from '../../../shared/interfaces/api/movimentacao-detalhes.interface';

@Injectable({
  providedIn: 'root'
})
export class MovimentacoesService {
  private readonly API_BASE = environment.useProxy ? '/api-cors/movimentacoes' : `${environment.apiUrl}/movimentacoes`;
  // private readonly API_BASE = `http://localhost:5010/movimentacoes` // Usa em desenvolvimento para API local
  private readonly usaMock: boolean = environment.useMock;

  // Injeções de dependências
  private http = inject(HttpClient);
  private authService = inject(AuthService);
  private mockDataService = inject(MockDataService);

  constructor() {}

  private buildHeaders(tenantId: string): HttpHeaders {
    const token = this.authService.getToken();
    let headers = new HttpHeaders({ 'x-tenant-id': tenantId });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }
    return headers;

  }

  todos(tenantId: string): Observable<Movimentacao[]> {
    if(this.usaMock) 
      return of(this.mockDataService.mockMovimentacoes());
    
    const headers = this.buildHeaders(tenantId);
    return this.http.get<Movimentacao[]>(`${this.API_BASE}`, { headers });
  }

  getById(id: number, tenantId: string): Observable<Movimentacao[]> {
    const headers = this.buildHeaders(tenantId);
    return this.http.get<Movimentacao[]>(`${this.API_BASE}/${id}`, { headers });
  }

  criar(agregador: Movimentacao, tenantId: string): Observable<Movimentacao> {
    const headers = this.buildHeaders(tenantId);
    return this.http.post<Movimentacao>(`${this.API_BASE}`, agregador, { headers });
  }

  atualizar(id: number, agregador: Movimentacao, tenantId: string): Observable<Movimentacao[]> {
    const headers = this.buildHeaders(tenantId);
    return this.http.put<Movimentacao[]>(`${this.API_BASE}/${id}`, agregador, { headers });
  }

  deletar(id: number, tenantId: string): Observable<void> {
    const headers = this.buildHeaders(tenantId);
    return this.http.delete<void>(`${this.API_BASE}/${id}`, { headers });
  }

   obterMovimentacoesDetalhes(id: number | null, tenantId: string): Observable<any> {
    if(this.usaMock) {
      const detalhe = this.mockDataService.mockDetalhesMovimentacoes().find(
        (detalhe: any) => detalhe.idMovimentacao.toString() === id
      );
      if (detalhe) {
        return of(detalhe);
      } else {
        return throwError(() => new Error('MovimentacoesDetalhes not found'));
      }
    }
    
    const headers = this.buildHeaders(tenantId);
    return this.http.get<any>(`${this.API_BASE}/${id}/detalhes`, { headers });
  }

  obterTodosItensMovimentacao(idAgregador: number , idMovimentacao: number | null, tenantId: string): Observable<any> {
    const headers = this.buildHeaders(tenantId);
    return this.http.get<any>(`${this.API_BASE}/${idAgregador}/itens?excluirMovimentacao=${idMovimentacao}`, { headers });
  }

}
