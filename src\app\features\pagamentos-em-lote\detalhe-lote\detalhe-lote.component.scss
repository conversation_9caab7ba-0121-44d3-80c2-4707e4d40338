.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.btn-sm {
  font-size: 0.75rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-muted {
  font-size: 0.875rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .col-md-2, .col-md-3 {
    margin-bottom: 1rem;
  }
  
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
  
  .btn {
    width: 100%;
  }
}

// AG Grid custom styles
::ng-deep {
  .ag-theme-alpine {
    .ag-cell {
      display: flex;
      align-items: center;
    }
    
    .ag-header-cell-text {
      font-weight: 600;
    }
  }
}
