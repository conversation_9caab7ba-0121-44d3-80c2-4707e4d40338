import { Directive, ElementRef, Input, OnChanges, Renderer2, SimpleChanges } from '@angular/core';

@Directive({
	selector: '[appCellRenderer]',
	standalone: true,
})
export class CellRendererDirective implements OnChanges {
	@Input('appCellRenderer') cellRenderer!: (params: any) => HTMLElement | string;
	@Input() cellRendererParams: any;

	constructor(
		private el: ElementRef,
		private renderer: Renderer2,
	) {}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['cellRenderer'] || changes['cellRendererParams']) {
			this.render();
		}
	}

	private render(): void {
		const parentElement = this.el.nativeElement;
		// Limpa o conteúdo anterior
		for (const child of Array.from(parentElement.childNodes)) {
			this.renderer.removeChild(parentElement, child);
		}

		if (this.cellRenderer) {
			const result = this.cellRenderer(this.cellRendererParams);
			if (typeof result === 'string') {
				parentElement.innerHTML = result;
			} else if (result instanceof HTMLElement) {
				this.renderer.appendChild(parentElement, result);
			}
		}
	}
}
