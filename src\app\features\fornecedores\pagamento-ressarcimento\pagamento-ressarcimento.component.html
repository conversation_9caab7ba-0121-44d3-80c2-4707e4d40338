<div class="auth-wrapper">
	<div class="auth-content">
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					<img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
				</div>
				<h3 class="mb-4">Pagamento Ressarcimento</h3>
				<span class="text-muted mb-2 d-block">Preencha os dados abaixo para registrar o pagamento ou agendar um ressarcimento.</span>
			   <span class="text-muted mb-4 d-block">Valor do ressarcimento <b>R$ {{valorParam}}</b>.</span>
				<form [formGroup]="formulario" (ngSubmit)="enviar()" class="modern-form">
				   <div class="d-flex flex-column align-items-center mb-4">
					   <label class="mb-2 fw-bold">Tipo de operação</label>
					   <div class="d-flex justify-content-center gap-3">
						   <div class="form-check">
							   <input class="form-check-input" type="radio" id="realizado" value="realizado" formControlName="tipoPagamento">
							   <label class="form-check-label" for="realizado">Pagamento realizado</label>
						   </div>
						   <div class="form-check">
							   <input class="form-check-input" type="radio" id="agendar" value="agendar" formControlName="tipoPagamento">
							   <label class="form-check-label" for="agendar">Agendar pagamento</label>
						   </div>
					   </div>
				   </div>

				   <div class="mb-4 d-flex flex-column col-6" style="justify-self: center;" *ngIf="formulario.get('tipoPagamento')?.value === 'realizado'">
					   <label for="dataPagamento" class="form-label fw-bold text-start w-100">Data do pagamento</label>
					   <input type="date" id="dataPagamento" class="form-control modern-input" formControlName="dataPagamento" />
				   </div>

				   <div class="mb-4 d-flex flex-column col-6" style="justify-self: center;" *ngIf="formulario.get('tipoPagamento')?.value === 'agendar'">
					   <label for="dataAgendada" class="form-label fw-bold text-start w-100">Data agendada para pagamento</label>
					   <input type="date" id="dataAgendada" class="form-control modern-input" formControlName="dataAgendada" />
				   </div>

				   <div class="mb-4 d-flex flex-column col-6" style="justify-self: center;">
					   <label for="valor" class="form-label fw-bold text-start w-100">Valor do pagamento</label>
					   <div class="input-group">
						   <span class="input-group-text">R$</span>
						   <input type="text" id="valor" class="form-control modern-input" formControlName="valor" placeholder="0,00" currencyBr/>
					   </div>
				   </div>

				    <div class="mb-4 d-flex flex-column col-10" style="justify-self: center;" *ngIf="formulario.get('tipoPagamento')?.value === 'realizado'">
						<label for="comprovante" class="form-label fw-bold text-start w-100">Comprovante de pagamento</label>
						<input type="file" id="comprovante" class="form-control modern-input" (change)="onComprovanteChange($event)" accept="image/*,application/pdf" />
				   </div>

				   <div class="alert alert-danger" *ngIf="errorMessage">
					   <i class="feather icon-alert-circle"></i>
					   {{ errorMessage }}
				   </div>

				   <button class="btn btn-primary w-25 mb-4" [disabled]="formulario.invalid || isLoading()">
					   <span *ngIf="!isLoading()">Enviar</span>
					   <span *ngIf="isLoading()">
						   <span class="spinner-border spinner-border-sm me-2" role="status"></span>
						   Carregando...
					   </span>
				   </button>
			   </form>
			</div>
		</div>
	</div>
</div>
