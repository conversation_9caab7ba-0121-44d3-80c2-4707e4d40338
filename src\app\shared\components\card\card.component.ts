import { Component, ElementRef, Input, OnInit, TemplateRef, inject, contentChild, input } from '@angular/core';
import { animate, AUTO_STYLE, state, style, transition, trigger } from '@angular/animations';
import { CommonModule, NgTemplateOutlet } from '@angular/common';

// bootstrap import
import { NgbDropdownConfig } from '@ng-bootstrap/ng-bootstrap';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';

@Component({
	selector: 'app-card',
	imports: [CommonModule, NgbDropdownModule, NgTemplateOutlet],
	templateUrl: './card.component.html',
	styleUrls: ['./card.component.scss'],
	providers: [NgbDropdownConfig],
	animations: [
		trigger('collapsedCard', [
			state(
				'collapsed, void',
				style({
					overflow: 'hidden',
					height: '0px',
				})
			),
			state(
				'expanded',
				style({
					overflow: 'hidden',
					height: AUTO_STYLE,
				})
			),
			transition('collapsed <=> expanded', [animate('400ms ease-in-out')]),
		]),
		trigger('cardRemove', [
			state(
				'open',
				style({
					opacity: 1,
				})
			),
			state(
				'closed',
				style({
					opacity: 0,
					display: 'none',
				})
			),
			transition('open <=> closed', animate('400ms')),
		]),
	],
})
export class CardComponent implements OnInit {
	@Input() cardTitle: string;
	@Input() cardClass: string | undefined;
	blockClass = input<string>();
	headerClass = input<string>();
	@Input() options: boolean;
	@Input() hidHeader: boolean;
	@Input() customHeader: boolean;
	footerClass = input<string>();
	footerTemplate = contentChild<TemplateRef<ElementRef>>('footerTemplate');

	animation: string | undefined;
	fullIcon: string;
	isAnimating: boolean;
	collapsedCard: string;
	collapsedIcon: string;
	loadCard: boolean;
	cardRemove: string;

	constructor() {
		const config = inject(NgbDropdownConfig);

		config.placement = 'bottom-right';
		this.customHeader = false;
		this.options = true;
		this.hidHeader = false;
		this.cardTitle = 'Card Title';
		this.fullIcon = 'icon-maximize';
		this.isAnimating = false;

		this.collapsedCard = 'expanded';
		this.collapsedIcon = 'icon-minus';

		this.loadCard = false;

		this.cardRemove = 'open';
	}

	// life cycle event
	ngOnInit() {
		if (!this.options || this.hidHeader || this.customHeader) {
			this.collapsedCard = 'false';
		}
	}

	fullCardToggle(animation: any, status: boolean) {
		animation = this.cardClass === 'full-card' ? 'zoomOut' : 'zoomIn';
		this.fullIcon = this.cardClass === 'full-card' ? 'icon-maximize' : 'icon-minimize';
		this.cardClass = this.cardClass === 'full-card' ? this.cardClass : 'full-card';
		if (status) {
			this.animation = animation;
		}
		this.isAnimating = true;

		setTimeout(() => {
			this.cardClass = animation === 'zoomOut' ? '' : this.cardClass;
			const bodyElement = document.querySelector('body');
			if (this.cardClass === 'full-card' && bodyElement) {
				bodyElement.style.overflow = 'hidden';
			} else if (bodyElement) {
				bodyElement.removeAttribute('style');
			}
		}, 500);
	}

	collapsedCardToggle() {
		this.collapsedCard = this.collapsedCard === 'collapsed' ? 'expanded' : 'collapsed';
		this.collapsedIcon = this.collapsedCard === 'collapsed' ? 'icon-plus' : 'icon-minus';
	}

	cardRefresh() {
		this.loadCard = true;
		this.cardClass = 'card-load';
		const bodyElement = document.querySelector('body');
		if (bodyElement) {
			bodyElement.removeAttribute('style');
		}
		setTimeout(() => {
			this.loadCard = false;
			this.cardClass = 'expanded';
		}, 3000);
	}

	cardRemoveAction() {
		this.cardRemove = this.cardRemove === 'closed' ? 'open' : 'closed';
	}
}
