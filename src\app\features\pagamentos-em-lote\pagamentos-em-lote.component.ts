import { Component, inject, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ColDef } from 'ag-grid-community';
import { TabelaPadraoComponent } from '../../shared/components/tabelas/tabela-padrao/tabela-padrao.component';
import { SelectSimplesComponent } from '../../shared/components/selects/select-simples/select-simples.component';
import { OpcaoSelect } from '../../shared/components/selects/select.enum';
import { PagamentosLotesService } from '../../core/services/api/pagamentos-lotes.service';
import { LoteResumo, LotesRequest } from '../../shared/interfaces/api/pagamentos-lotes.interface';
import { StatusLoteImportacao, statusLoteOpcoes } from '../../shared/enums/status-lote-importacao.enum';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImportarExcelModalComponent } from './importar-excel-modal.component';

@Component({
  selector: 'app-pagamentos-em-lote',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, MatSnackBarModule, TabelaPadraoComponent, SelectSimplesComponent],
  templateUrl: './pagamentos-em-lote.component.html',
  styleUrl: './pagamentos-em-lote.component.scss'
})
export class PagamentosEmLoteComponent implements OnInit {
  private fb = inject(FormBuilder);
  private service = inject(PagamentosLotesService);
  private snackBar = inject(MatSnackBar);
  private modal = inject(NgbModal);
  private router = inject(Router);

  filtros = this.fb.group({
    dataInicial: this.fb.control<string | null>(null),
    dataFinal: this.fb.control<string | null>(null),
    usuario: this.fb.control<string>(''),
    status: this.fb.control<StatusLoteImportacao | ''>('')
  });

  opcoesStatus: OpcaoSelect[] = statusLoteOpcoes();
  carregando = signal(false);
  lotes = signal<LoteResumo[]>([]);

  columnDefs: ColDef[] = [
    { field: 'id', headerName: 'ID do Lote', minWidth: 120 },
    { field: 'dataProcessamento', headerName: 'Data/Hora do processamento', minWidth: 200, valueFormatter: p => p.value ? new Date(p.value).toLocaleString('pt-BR') : '' },
    { field: 'usuarioProcessamento', headerName: 'Usuário', minWidth: 160 },
    { field: 'nomeArquivo', headerName: 'Arquivo', minWidth: 180 },
    { field: 'totalLinhasLidas', headerName: 'Lidos', minWidth: 100 },
    { field: 'totalProgramados', headerName: 'Programados', minWidth: 120 },
    { field: 'totalLiquidados', headerName: 'Liquidados', minWidth: 120 },
    { field: 'totalCriados', headerName: 'Criados', minWidth: 100 },
    { field: 'totalIgnorados', headerName: 'Ignorados', minWidth: 110 },
    { field: 'totalDuplicados', headerName: 'Duplicados', minWidth: 110 },
    { field: 'totalErros', headerName: 'Erros', minWidth: 100 },
    { headerName: 'Status', minWidth: 140, cellRenderer: (p: any) => this.renderStatus(p.data?.status) },
    { headerName: 'Ações', minWidth: 220, cellRenderer: (p: any) => this.renderAcoes(p.data) }
  ];

  ngOnInit(): void {
    this.buscar();
  }

  buscar(): void {
    const v = this.filtros.value;
    const req: LotesRequest = {
      dataInicial: v.dataInicial ? new Date(v.dataInicial) : null,
      dataFinal: v.dataFinal ? new Date(v.dataFinal) : null,
      usuario: v.usuario || '',
      status: (v.status as any) || ''
    };
    this.carregando.set(true);
    this.service.listarLotes(req).subscribe({
      next: r => { this.lotes.set(r.lotes || []); this.carregando.set(false); },
      error: () => { this.lotes.set([]); this.carregando.set(false); }
    });
  }

  limpar(): void {
    this.filtros.reset({ dataInicial: null, dataFinal: null, usuario: '', status: '' });
    this.buscar();
  }

  abrirImportar(): void {
    const ref = this.modal.open(ImportarExcelModalComponent, { backdrop: 'static', keyboard: false, centered: true, windowClass: 'modal-base-custom' });
    ref.result.then((res: any) => {
      if (res?.sucesso) {
        this.snackBar.open('Importação enviada com sucesso.', '', { duration: 4000, panelClass: ['snackbar-success'] });
        this.buscar();
      }
    }).catch(() => {});
  }

  baixar(id: number): void {
    this.service.downloadArquivo(id).subscribe(b => {
      const url = URL.createObjectURL(b);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lote-${id}.xlsx`;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      a.remove();
    });
  }

  verDetalhes(): void {
    this.router.navigate(['/pagina-inicial']);
  }

  private renderStatus(status: StatusLoteImportacao | string): HTMLElement {
    const span = document.createElement('span');
    span.className = 'badge';
    let cls = 'bg-secondary';
    if (status === StatusLoteImportacao.Concluido) cls = 'bg-success';
    else if (status === StatusLoteImportacao.ConcluidoComErros) cls = 'bg-warning';
    else if (status === StatusLoteImportacao.Falha) cls = 'bg-danger';
    span.classList.add(cls);
    span.textContent = String(status);
    return span;
  }

  private renderAcoes(row: LoteResumo): HTMLElement {
    const container = document.createElement('div');
    container.style.display = 'flex';
    container.style.gap = '8px';

    const btnDetalhes = document.createElement('button');
    btnDetalhes.className = 'btn btn-outline-secondary btn-sm';
    btnDetalhes.innerHTML = '<i class="feather icon-eye"></i> Ver Detalhes';
    btnDetalhes.addEventListener('click', (e) => { e.stopPropagation(); this.verDetalhes(); });

    const btnBaixar = document.createElement('button');
    btnBaixar.className = 'btn btn-primary btn-sm';
    btnBaixar.innerHTML = '<i class="feather icon-download"></i> Baixar arquivo processado';
    btnBaixar.addEventListener('click', (e) => { e.stopPropagation(); this.baixar(row.id); });

    container.appendChild(btnDetalhes);
    container.appendChild(btnBaixar);
    return container;
  }
}

