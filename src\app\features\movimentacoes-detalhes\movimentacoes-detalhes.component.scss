@import 'src/scss/_variables.scss';


.info-label {
	font-size: 18px;
	color: #212529bf;
	font-weight: 300;
	// padding-right: 5px;
}

.cards-pagamentos-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}


.icone-transparente {
	position: relative;
    color: #fff;
    opacity: 0.3;
    font-size: 60px;
    right: -12px;
    bottom: -7px;
	z-index: -1;
}

.icone-transparente-detalhes {
	display: block;
    position: absolute;
    color: #535353;
	opacity: 0.06;
    font-size: 60px;
    right: 14px;
    bottom: 10px;
    z-index: 0 !important;

}

.titulo-card {
	display: flex;
    position: relative;
    justify-content: center;
	color: #212529bf;
	margin-top: -2px;
	padding: 0;
	font-size: 14px;
	text-align: center;
}

.linha{
	width: 95%;
	// color: #000;
	// border: 1px solid #adadad3b;
	justify-self: center;
}

$border-card: 1px solid #7d8ba52c;

.border-doc{
	border-bottom: $border-card !important;
	border-top: $border-card !important;
	border-right: $border-card !important;
}

.accordion-item:last-of-type > .accordion-header .accordion-button.collapsed {
	background: #eff0f2 !important;
}

.btn-ressarcimento {
	display: flex;
    position: relative;
    justify-self: center;
    
    width: 155px;
    right: -48px;

	button {
		color: $primary-color;
		font-size: 11px;
		padding-top: 3px;
    	padding-bottom: 3px;

		&:hover {
			color:  #{darken($primary-color, 12%)};
			-webkit-transition: 500ms;
      		transition: 500ms;
		}
	}

}

.nota-icon{
	margin-right: 10px;
	cursor: pointer;
}


@media (min-width: 992px) {
  .cards-pagamentos-grid {
	grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1775px) {
  .cards-pagamentos-grid {
	grid-template-columns: repeat(4, 1fr);
	// gap: 32px;
  }
}

@media (max-width: 991.98px) {
  .cards-pagamentos-grid {
	grid-template-columns: 1fr;
  }
}