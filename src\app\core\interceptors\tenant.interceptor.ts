import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { AuthService } from '../auth/services/auth.service';
import { environment } from '../../../environments/environment';

export const tenantInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const usuarioAtual = authService.getUsuarioAtual();

  // Ignorar requisições para a rota de autenticação
  if (req.url.includes(`${environment.proxyPath}/auth/`)) {
    return next(req);
  }

  if (usuarioAtual && usuarioAtual.clienteId) {
    const clonedRequest = req.clone({
      setHeaders: {
        'x-tenant-id': usuarioAtual.clienteId
      }
    });
    return next(clonedRequest);
  } else {
    return next(req);
  }
};
