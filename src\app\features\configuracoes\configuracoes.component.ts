import { Component } from '@angular/core';
import { SelectMultiCheckboxComponent } from "../../shared/components/selects/select-multi-checkbox/select-multi-checkbox.component";
import { BreadcrumbsComponent } from "../../shared/components/breadcrumbs/breadcrumbs.component";
import { HttpClient } from '@angular/common/http';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-configuracoes',
  imports: [SelectMultiCheckboxComponent, BreadcrumbsComponent],
  templateUrl: './configuracoes.component.html',
  styleUrl: './configuracoes.component.scss'
})
export class ConfiguracoesComponent {
  opcoes = [
    { valor: 'empresa1', nome: 'Empresa 1' },
    { valor: 'empresa2', nome: 'Empresa 2' },
    { valor: 'empresa3', nome: 'Empresa 3' },
  ]
  selecionados: string[] = [];

  constructor(private http: HttpClient) {}

  onAlteracaoSelecao(event: any): void {
    console.log('Seleção alterada:', event);
  }

  zerarJsonMock() {
    // Primeiro, busca os dados do backup
    interface BackupResponse {
      movimentacoes: any;
      detalhesMovimentacoes: any;
      painelPrincipal: any;
      [key: string]: any;
    }
    this.http.get<BackupResponse>('http://localhost:3000/backup').subscribe((backupData) => {
      let detalhes = backupData.detalhesMovimentacoes || [];
      let painel = backupData.painelPrincipal || [];
      let movimentacoes = backupData.movimentacoes || [];

      detalhes.forEach((detalhe: any) => {
      console.log('detalhe :', detalhe);
        this.http.delete(`http://localhost:3000/detalhes-movimentacoes/${detalhe.id}`).subscribe(()=>{
          this.http.post('http://localhost:3000/detalhes-movimentacoes', detalhe).subscribe();
        });

      });

      // Atualiza os dados usando PUT para substituir completamente
      // forkJoin([
      //   this.http.post('http://localhost:3000/detalhes-movimentacoes', detalhes),
      //   this.http.post('http://localhost:3000/painel-principal', painel),
      //   this.http.post('http://localhost:3000/movimentacoes', movimentacoes)
      // ]).subscribe(() => {
      //   console.log('Dados resetados com sucesso');
      // });
    });
  }}