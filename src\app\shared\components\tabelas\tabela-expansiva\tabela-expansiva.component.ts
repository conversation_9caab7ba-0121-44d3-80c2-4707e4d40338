import { Component, Input, Output, EventEmitter, TemplateRef, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ColDef } from 'ag-grid-community';
import { CellRendererDirective } from '../../../directives/cell-renderer.directive';
import { animate, state, style, transition, trigger } from '@angular/animations';

export interface TabelaExpansivaColuna {
	field: string;
	header: string;
	class?: string;
	cellTemplate?: TemplateRef<any>;
}
@Component({
	selector: 'app-tabela-expansiva',
	standalone: true,
	animations: [
		trigger('expandirLinha', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4,0.0,0.2,1)')),
		]),
	],
	imports: [CommonModule, CellRendererDirective],
	templateUrl: './tabela-expansiva.component.html',
	styleUrls: ['./tabela-expansiva.component.scss'],
	encapsulation: ViewEncapsulation.None,
})

export class TabelaExpansivaComponent {
	// --- Entradas (Inputs) ---
	@Input() dadosTabelaPrincipal: any[] = [];
	@Input() colunasTabelaPrincipal: ColDef[] = [];
	@Input() propriedadeDadosExpansao = 'versoes'; 
	@Input() colunasTabelaExpansao: ColDef[] = [];

	// --- Saídas (Outputs) ---
	@Output() acao = new EventEmitter<{ acao: string; itemPrincipal: any; itemExpansao?: any }>();

	public linhaExpandida: any = null;
	public menuAberto: any = null;

	get processedColumnDefs(): ColDef[] {
		return this.colunasTabelaPrincipal.map((col) => {
			if (col.cellRenderer && !col.cellStyle) {
				return {
					...col,
					cellStyle: {
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						height: '100%',
						cursor: 'pointer',
						fontSize: '16px',
					},
				};
			}
			return col;
		});
	}

	public colunasDefs: ColDef[] = this.processedColumnDefs;

	public toggleExpandir(item: any): void {
		this.linhaExpandida = this.linhaExpandida === item ? null : item;
	}

	public toggleMenuDropdown(item: any, event: MouseEvent): void {
		event.stopPropagation();
		this.menuAberto = this.menuAberto === item ? null : item;
	}

	public executarAcao(acao: string, itemPrincipal: any, itemExpansao?: any): void {
		this.acao.emit({ acao, itemPrincipal, itemExpansao });
		this.menuAberto = null;
	}

	public obterValor(item: any, coluna: ColDef | TabelaExpansivaColuna): any {
		if (coluna.field && item[coluna.field]) {
			return item[coluna.field];
		}
		return '-';
	}

	public formataValor(valor: any, coluna: ColDef): string {
		let valorFormatado = '';

		const valueFormatterStr = coluna.valueFormatter?.toString() as string;

		if (valueFormatterStr && typeof valueFormatterStr === 'string') {
			try {
				// Cria a função dinamicamente
				const formatterFn = new Function('params', `return (${valueFormatterStr})(params);`);

				// Executa a função simulando o objeto `params` como no AG Grid
				valorFormatado = formatterFn({ value: valor });
			} catch (error) {
				console.error('Erro ao processar valueFormatter:', error);
				valorFormatado = String(valor ?? '-');
			}
		} else {
			valorFormatado = String(valor ?? '-');
		}

		return valorFormatado;
	}

	public formataComQuebraDeLinha(valor: any): string {
		if (Array.isArray(valor)) {
			let valorAtualizado = valor.join(',<br>');
			valorAtualizado = valorAtualizado.replace(/,/g, '');
			return valorAtualizado;
		}
		return valor;
	}

	trackByFn(index: number, item: any): any {
		return item.id || item.codigo || index;
	}
}
