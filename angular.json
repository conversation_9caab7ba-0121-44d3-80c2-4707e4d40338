{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"zin-frontend-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "ngHtml": false, "type": "component"}, "@schematics/angular:service": {"type": "service"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["apexcharts"], "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/fake-data"], "styles": ["node_modules/bootstrap/scss/bootstrap.scss", "src/styles.scss"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "12kB"}], "outputHashing": "all", "optimization": true, "buildOptimizer": true, "aot": true, "extractLicenses": true, "vendorChunk": false, "namedChunks": false}, "mock": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.mock.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "zin-frontend-angular:build:production"}, "development": {"buildTarget": "zin-frontend-angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "zin-frontend-angular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"]}}}}}, "cli": {"analytics": "b0543dbe-01c5-4e7e-aa8a-837fd863bf39", "schematicCollections": ["@angular-eslint/schematics"]}}