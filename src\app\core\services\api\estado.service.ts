import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { EstadosResponse } from '../../../shared/interfaces/api/estado.interface';

@Injectable({
  providedIn: 'root'
})
export class EstadoService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  listar(): Observable<EstadosResponse> {
    return this.http.get<EstadosResponse>(`${this.API_BASE}/estados`);
  }
}