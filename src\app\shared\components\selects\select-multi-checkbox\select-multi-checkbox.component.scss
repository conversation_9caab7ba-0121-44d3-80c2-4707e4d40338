.multi-select-container {
  position: relative;
  width: 100%;
  max-width: 350px;
  font-family: inherit;
}

.multi-select-input {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  min-height: 38px;
  background: #fff;
  cursor: pointer;
  flex-wrap: wrap;
}

.multi-select-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.chip {
  background: #e3e3e3;
  border-radius: 12px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-right: 2px;
}

.chip-close {
  margin-left: 4px;
  cursor: pointer;
  font-weight: bold;
}

.multi-select-arrow {
  margin-left: 8px;
  font-size: 16px;
}

.multi-select-clear {
  margin-left: 8px;
  color: #888;
  cursor: pointer;
  font-size: 18px;
}

.multi-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 0 0 4px 4px;
  z-index: 10;
  max-height: 220px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 6px 0;
}

.multi-select-opcao {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 15px;
}

.multi-select-opcao:hover {
  background: #e9ecef;
}

input[type="text"] {
  border: none;
  outline: none;
  background: transparent;
  min-width: 80px;
  flex: 1;
  font-size: 15px;
  padding: 2px 0;
}
