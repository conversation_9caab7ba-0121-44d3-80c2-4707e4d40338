<app-card cardTitle="Movimentações" blockClass="p-0">
	<div class="row mb-2 p-4">
		@for (filtro of filtrosApi; track filtro) {
			<div class="col-3">
				<label>Filtrar {{ filtro.label }}</label>
				<ng-container>
					<input
						type="text"
						class="form-control form-control-sm mb-4"
						[(ngModel)]="filtrosApi[$index].valor"
						placeholder="{{ filtro.label }}"
					/>
				</ng-container>
			</div>
		}
	</div>

	@if (erro()) {
		<div class="alert alert-danger">
			{{ erro() }}
		</div>
	}

	<div class="p-4">
		@if (showGrid) {
			<app-tabela-padrao
				[columnDefs]="columnDefs"
				[pagination]="true"
				[rowData]="dadosMovimentacoes()"
				(gridReady)="onGridReady()"
			/>
		}
	</div>
</app-card>
