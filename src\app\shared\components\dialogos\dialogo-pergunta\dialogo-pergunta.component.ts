import { Component, inject, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { BotaoConfig } from '../../../interfaces/dialogos/botao-config.interface';
import { ResultadoDialogo } from '../../../interfaces/dialogos/resultado-dialogo.interface';

export type tipoCorHeader = 'info' | 'erro' | 'sucesso' | 'padrao' | 'custom' | '';

@Component({
  selector: 'app-dialogo-pergunta',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dialogo-pergunta.component.html',
  styleUrls: ['./dialogo-pergunta.component.scss'],
})
export class DialogoPerguntaComponent {

  private activeModal = inject(NgbActiveModal);

  @Input() titulo: string = 'Confirmação';
  @Input() descricao: string = 'Deseja confirmar esta ação?';
  @Input() mostrarBotaoCancelar: boolean = true;
  @Input() botaoConfirmar: BotaoConfig = {
    texto: 'Sim',
    habilitaIcone: true,
    classe: 'btn-primary',
    icone: 'feather icon-check',
  };
  @Input() botaoCancelar: BotaoConfig = {
    texto: 'Não',
    habilitaIcone: true,
    classe: 'btn-outline-dark',
    icone: 'feather icon-x',
  };

  @Input() corHeader: tipoCorHeader = '';

  constructor() {}

  onClick(click: boolean): void {
    const resultado: ResultadoDialogo = {
      confirmado: click,
      dados: null,
    };
    this.activeModal.close(resultado);
  }

 
}

