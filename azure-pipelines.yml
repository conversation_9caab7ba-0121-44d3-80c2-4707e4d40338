# Azure DevOps Pipeline - Zin Frontend Angular
# Versão otimizada seguindo template padrão
# Criado: $(Get-Date -Format "yyyy-MM-dd")
# Aplicação: Angular 20.x SPA

name: $(majorVersion).$(minorVersion).$(patchVersion)-$(Rev:r)

trigger:
  branches:
    include:
    - azure-pipelines
    - desenvolvimento
    - qa
    - release/*
  paths:
    include:
    - '*'
    exclude:
    - README.md
    - docs/*
    - '*.md'

variables:
  # === CONFIGURAÇÃO DA APLICAÇÃO ===
  applicationName: 'zin-frontend-angular'
  applicationDisplayName: 'Zin Frontend Angular'
  applicationDescription: 'Aplicação frontend Angular 20.x para sistema Zin'
  
  # === VERSIONAMENTO ===
  majorVersion: 1
  minorVersion: 0
  patchVersion: $[counter(format('{0}.{1}', variables['majorVersion'], variables['minorVersion']), 1)]
  buildVersion: $(majorVersion).$(minorVersion).$(patchVersion)
  fullVersion: $(buildVersion)-$(Build.BuildId)
  
  # === AMBIENTES E CONFIGURAÇÕES ===
  nodeVersion: '20.x'
  angularVersion: '20.x'
  buildConfiguration: 'production'
  
  # === CAMINHOS E ARTEFATOS ===
  workingDirectory: '$(Build.SourcesDirectory)'
  outputPath: '$(workingDirectory)/dist'
  packageStoragePath: 'C:\Packages\Zin-Frontend\packages'
  artifactName: '$(applicationName)-$(buildVersion)-$(Build.BuildId)'
  zipPath: '$(packageStoragePath)\$(artifactName).zip'
  changelogPath: '$(workingDirectory)\CHANGELOG.md'
  
  # === AGENTE DE BUILD ===
  buildAgent: 'agent_pop2'

pool:
  name: 'POP2'
  demands: 
  - agent.name -equals $(buildAgent)

stages:
- stage: BuildAndPackage
  displayName: 'Build e Empacotamento'
  
  jobs:
  - job: Build
    displayName: 'Build $(applicationDisplayName)'
    timeoutInMinutes: 30
    
    steps:
    # === CONFIGURAÇÃO INICIAL ===
    - checkout: self
      displayName: 'Checkout do código'
      clean: true
      persistCredentials: true

    - task: NodeTool@0
      displayName: 'Configurar Node.js $(nodeVersion)'
      inputs:
        versionSource: 'spec'
        versionSpec: '$(nodeVersion)'
        
    - task: PowerShell@2
      displayName: 'Inicialização do ambiente'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== INICIALIZAÇÃO DO AMBIENTE ==="
          Write-Host "Aplicação: $(applicationDisplayName)"
          Write-Host "Versão: $(fullVersion)"
          Write-Host "Configuração: $(buildConfiguration)"
          Write-Host "Node.js: $(nodeVersion)"
          Write-Host "Angular: $(angularVersion)"
          Write-Host "Agente: $(buildAgent)"
          Write-Host ""
          
          # Detectar estrutura do projeto
          Write-Host "Detectando estrutura do projeto..."
          $sourceDir = "$(Build.SourcesDirectory)"
          Write-Host "Diretório raiz: $sourceDir"
          
          # Verificar se é estrutura direta (package.json na raiz)
          if (Test-Path "$sourceDir\package.json") {
            Write-Host "✓ Estrutura direta - package.json encontrado na raiz"
            $workingDir = $sourceDir
          }
          # Verificar se é estrutura com subpasta Zin.Front.Angular
          elseif (Test-Path "$sourceDir\Zin.Front.Angular\package.json") {
            Write-Host "✓ Estrutura com subpasta - package.json encontrado em Zin.Front.Angular"
            $workingDir = "$sourceDir\Zin.Front.Angular"
          }
          else {
            Write-Host "Explorando estrutura de diretórios..."
            Get-ChildItem $sourceDir -Recurse -Name "package.json" | ForEach-Object { Write-Host "Encontrado: $_" }
            Write-Error "✗ package.json não encontrado"
            exit 1
          }
          
          # Definir working directory dinâmico
          Write-Host "##vso[task.setvariable variable=detectedWorkingDirectory]$workingDir"
          Set-Location $workingDir
          Write-Host "Diretório de trabalho definido: $workingDir"
          
          # Verificar estrutura Angular
          if (Test-Path "angular.json") {
            Write-Host "✓ angular.json encontrado"
          } else {
            Write-Error "✗ angular.json não encontrado em $workingDir"
            exit 1
          }
          
          # Criar diretório de artefatos se não existir
          $packageDir = "$(packageStoragePath)"
          if (-not (Test-Path $packageDir)) {
            New-Item -ItemType Directory -Path $packageDir -Force
            Write-Host "Diretório de artefatos criado: $packageDir"
          }

    # === DEPENDÊNCIAS ===
    - task: PowerShell@2
      displayName: 'Instalar dependências'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== INSTALAÇÃO DE DEPENDÊNCIAS ==="
          
          # Usar working directory detectado
          Set-Location "$(detectedWorkingDirectory)"
          Write-Host "Working Directory: $(Get-Location)"
          
          # Verificar versões
          Write-Host "Node.js: $(node --version)"
          Write-Host "npm: $(npm --version)"
          
          # Limpar cache se necessário
          npm cache clean --force
          
          # Instalar dependências
          Write-Host "Instalando dependências com npm ci..."
          npm ci --legacy-peer-deps --no-audit --no-fund
          
          Write-Host "Dependências instaladas com sucesso"

    # === ATUALIZAÇÃO DA VERSÃO ===
    - task: PowerShell@2
      displayName: 'Atualizar versão no package.json'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== ATUALIZAÇÃO DE VERSÃO ==="
          
          # Usar working directory detectado
          Set-Location "$(detectedWorkingDirectory)"
          
          # Ler package.json
          $packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
          $oldVersion = $packageJson.version
          
          # Atualizar versão
          $packageJson.version = "$(buildVersion)"
          $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json"
          
          Write-Host "Versão atualizada: $oldVersion → $(buildVersion)"

    # === BUILD DA APLICAÇÃO ===
    - task: PowerShell@2
      displayName: 'Build Angular'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== BUILD DA APLICAÇÃO ==="
          
          # Usar working directory detectado
          Set-Location "$(detectedWorkingDirectory)"
          Write-Host "Build Directory: $(Get-Location)"
          
          # Definir variáveis de ambiente
          $env:BUILD_VERSION = "$(buildVersion)"
          $env:BUILD_ID = "$(Build.BuildId)"
          $env:BUILD_DATE = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
          $env:BUILD_BRANCH = "$(Build.SourceBranchName)"
          $env:BUILD_COMMIT = "$(Build.SourceVersion)"
          
          # Verificar Angular CLI
          if (-not (Get-Command "ng" -ErrorAction SilentlyContinue)) {
            Write-Host "Instalando Angular CLI..."
            npm install -g @angular/cli@latest
          }
          
          Write-Host "Angular CLI: $(ng version --json | ConvertFrom-Json | Select-Object -ExpandProperty '@angular/cli')"
          
          # Build da aplicação
          Write-Host "Executando ng build --configuration $(buildConfiguration)..."
          ng build --configuration $(buildConfiguration)
          
          if ($LASTEXITCODE -ne 0) {
            Write-Error "Falha no build da aplicação"
            exit 1
          }
          
          # Definir output path dinâmico
          $outputPath = "$(Get-Location)\dist"
          Write-Host "##vso[task.setvariable variable=detectedOutputPath]$outputPath"
          Write-Host "Output path definido: $outputPath"
          
          Write-Host "Build concluído com sucesso"

    # === MANIFESTO DE VERSÃO ===
    - task: PowerShell@2
      displayName: 'Criar manifesto de versão'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== CRIAÇÃO DO MANIFESTO ==="
          
          # Usar output path detectado
          Set-Location "$(detectedOutputPath)"
          Write-Host "Output Directory: $(Get-Location)"
          
          $manifest = @{
              application = @{
                  name = "$(applicationDisplayName)"
                  version = "$(buildVersion)"
                  fullVersion = "$(fullVersion)"
                  type = "SPA"
                  framework = "Angular $(angularVersion)"
              }
              build = @{
                  id = "$(Build.BuildId)"
                  number = "$(Build.BuildNumber)"
                  date = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                  agent = "$(buildAgent)"
                  triggered_by = "$(Build.RequestedFor)"
                  configuration = "$(buildConfiguration)"
              }
              source = @{
                  repository = "$(Build.Repository.Name)"
                  branch = "$(Build.SourceBranchName)"
                  commit = "$(Build.SourceVersion)"
                  commit_message = "$(Build.SourceVersionMessage)"
              }
              runtime = @{
                  node_version = "$(nodeVersion)"
                  angular_version = "$(angularVersion)"
                  build_tool = "Angular CLI"
              }
          }
          
          # Salvar manifestos
          $manifest | ConvertTo-Json -Depth 10 | Set-Content "deployment-manifest.json" -Encoding UTF8
          "$(buildVersion)" | Set-Content "VERSION" -Encoding ASCII -NoNewline
          
          # Criar arquivo de versão para runtime
          if (Test-Path "assets") {
            "window.APP_VERSION = " + ($manifest | ConvertTo-Json -Compress) + ";" | 
            Set-Content "assets/version.js" -Encoding UTF8
          }
          
          Write-Host "Manifesto criado para versão $(buildVersion)"

    # === ATUALIZAÇÃO DO CHANGELOG ===
    - task: PowerShell@2
      displayName: 'Atualizar CHANGELOG.md'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== ATUALIZAÇÃO DO CHANGELOG ==="
          
          # Usar working directory detectado para CHANGELOG
          $changelogPath = "$(detectedWorkingDirectory)\CHANGELOG.md"
          $version = "$(buildVersion)"
          $buildId = "$(Build.BuildId)"
          $date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
          $branch = "$(Build.SourceBranchName)"
          $commit = "$(Build.SourceVersion)"
          $commitMsg = "$(Build.SourceVersionMessage)"
          
          Write-Host "CHANGELOG path: $changelogPath"
          
          # Criar entrada do changelog
          $newEntry = @"
          ## [$version] - $date (Build: $buildId)
          
          **Branch**: $branch  
          **Commit**: $commit  
          **Mensagem**: $commitMsg  
          **Configuração**: $(buildConfiguration)  
          **Node.js**: $(nodeVersion)  
          **Angular**: $(angularVersion)  
          
          ### Informações do Build
          - Gerado automaticamente pelo pipeline
          - Artefato: $(artifactName)
          - Agente: $(buildAgent)
          - Solicitado por: $(Build.RequestedFor)
          
          ---
          
          "@
          
          # Atualizar ou criar CHANGELOG
          if (Test-Path $changelogPath) {
            $currentContent = Get-Content $changelogPath -Raw
            $newEntry + $currentContent | Set-Content $changelogPath -Encoding UTF8
            Write-Host "CHANGELOG.md atualizado com versão $version"
          } else {
            @"
          # Changelog - $(applicationDisplayName)
          
          Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.
          
          $newEntry
          "@ | Set-Content $changelogPath -Encoding UTF8
            Write-Host "CHANGELOG.md criado com versão $version"
          }

    # === EMPACOTAMENTO ===
    - task: ArchiveFiles@2
      displayName: 'Criar artefato'
      inputs:
        rootFolderOrFile: '$(detectedOutputPath)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(zipPath)'
        replaceExistingArchive: true

    - task: PublishBuildArtifacts@1
      displayName: 'Publicar artefato'
      inputs:
        PathtoPublish: '$(zipPath)'
        ArtifactName: '$(artifactName)'
        publishLocation: 'Container'

    # === TAGS E FINALIZAÇÃO ===
    - task: PowerShell@2
      displayName: 'Aplicar tags e finalizar'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== FINALIZAÇÃO ==="
          
          # Aplicar tags
          Write-Host "##vso[build.addbuildtag]v$(buildVersion)"
          Write-Host "##vso[build.addbuildtag]$(Build.SourceBranchName)"
          Write-Host "##vso[build.addbuildtag]angular"
          Write-Host "##vso[build.addbuildtag]spa"
          Write-Host "##vso[build.addbuildtag]frontend"
          Write-Host "##vso[build.addbuildtag]ready-for-deploy"
          
          # Atualizar variáveis de build
          Write-Host "##vso[build.updatebuildnumber]$(applicationName)-$(buildVersion)-$(Build.BuildId)"
          
          Write-Host ""
          Write-Host "=== RESUMO DO BUILD ==="
          Write-Host "Aplicação: $(applicationDisplayName)"
          Write-Host "Versão: $(fullVersion)"
          Write-Host "Artefato: $(artifactName)"
          Write-Host "Caminho: $(zipPath)"
          Write-Host "Status: Pronto para deploy"
          Write-Host ""
          Write-Host "Pipeline concluído com sucesso!"
