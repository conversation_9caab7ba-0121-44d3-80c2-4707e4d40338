<div class="row">
  <div class="col-sm-12">
    <app-card #cardForm
      [cardTitle]="editando ? 'Editar Regra de Processamento' : 'Cadastro de Regras de Processamento'">
      <form *ngIf="regrasForm" [formGroup]="regrasForm" (ngSubmit)="salvarRegra()">

        <div class="row">
          <div class="col-md-6">
            <label>Nome da Regra</label>
            <input type="text" class="form-control" placeholder="Digite o nome da regra" formControlName="nomeRegra">
          </div>
          <div class="col-md-6">
            <label>Tipo de Processamento</label>
            <app-select-simples [opcoes]="tiposProcessamento"
              [valorSelecionado]="regrasForm.get('tipoProcessamento')?.value"
              (mudouValor)="onTipoProcessamentoChange($event)"></app-select-simples>
          </div>
        </div>

        <hr />

        <div class="row mt-2">
          <div class="col-md-4">
            <label>Tipo de Configuração</label>
            <app-select-simples [opcoes]="tiposConfiguracao"
              [valorSelecionado]="regrasForm.get('tipoConfiguracao')?.value"
              (mudouValor)="onTipoConfiguracaoChange($event)"></app-select-simples>
          </div>
          <div class="col-md-4">
            <label>Operador</label>
            <app-select-simples [opcoes]="operadores" [valorSelecionado]="regrasForm.get('operador')?.value"
              (mudouValor)="onOperadorChange($event)"></app-select-simples>
          </div>
          <div class="col-md-4">
            <label>Valor</label>
            <ng-container [ngSwitch]="tipagemAtual">
              <ng-container *ngSwitchCase="'ENUM|StatusVeiculo'">
                <app-select-simples [opcoes]="statusVeiculosOpcoes" [valorSelecionado]="regrasForm.get('valor')?.value"
                  (mudouValor)="onValorChange($event)"></app-select-simples>
              </ng-container>
              <ng-container *ngSwitchDefault>
                <input type="text" class="form-control" placeholder="Digite o valor" formControlName="valor">
              </ng-container>
            </ng-container>
          </div>
        </div>

        <div class="form-check mt-2">
          <input type="checkbox" class="form-check-input" formControlName="ativo" id="ativo">
          <label class="form-check-label" for="ativo">Ativo</label>
        </div>

        <div class="mt-3" *ngIf="!editando">
          <button type="button" class="btn btn-outline-primary me-2" (click)="adicionarRegraTemporaria()">
            Adicionar regra à lista
          </button>
          <button type="submit" class="btn btn-primary"
            [disabled]="regrasForm.invalid || regrasTemporarias.length === 0">
            Salvar Configuração
          </button>
        </div>

        <div class="mt-3" *ngIf="editando">
          <button type="submit" class="btn btn-primary me-2" [disabled]="regrasForm.invalid">
            Salvar Alterações
          </button>
          <button type="button" class="btn btn-secondary" (click)="cancelarEdicao()">
            Cancelar
          </button>
        </div>
        <div class="mt-3" *ngIf="editando">
          <h6>Regras desta configuração</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>Valor</th>
                  <th>Operador</th>
                  <th>Ativo</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let r of regrasTemporarias; let i = index" [class.table-active]="selectedRegraIndex === i">
                  <td>{{ r.Valor }}</td>
                  <td>{{ getOperadorLabel(r.Operador) }}</td>
                  <td>{{ r.Ativo ? 'Sim' : 'Não' }}</td>
                  <td class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary me-2"
                      (click)="editarRegraTemporaria(i)">Editar</button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                      (click)="removerRegraTemporaria(i)">Remover</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="mt-2">
            <button type="button" class="btn btn-outline-primary me-2" (click)="adicionarRegraTemporaria()">Adicionar
              nova regra</button>
            <button type="button" class="btn btn-primary" (click)="salvarConfiguracao()">Salvar configuração</button>
          </div>
        </div>

        <div class="mt-3" *ngIf="!editando && regrasTemporarias.length">
          <h6>Regras adicionadas</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>Valor</th>
                  <th>Operador</th>
                  <th>Ativo</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let r of regrasTemporarias; let i = index">
                  <td>{{ r.Valor }}</td>
                  <td>{{ getOperadorLabel(r.Operador) }}</td>
                  <td>{{ r.Ativo ? 'Sim' : 'Não' }}</td>
                  <td>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                      (click)="removerRegraTemporaria(i)">Remover</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </form>
    </app-card>
  </div>
</div>

<div class="row mt-4">
  <div class="col-sm-12">
    <app-card cardTitle="Regras de Processamento">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex gap-2">
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="expandAllConfiguracoes()">
            <i class="feather icon-chevron-down me-1"></i>
            Expandir Todas
          </button>
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="collapseAllConfiguracoes()"
            [disabled]="!hasAnyExpanded()">
            <i class="feather icon-chevron-up me-1"></i>
            Recolher Todas
          </button>
        </div>
        <small class="text-muted">
          {{ configuracoes.length }} configurações encontradas
        </small>
      </div>
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
          </thead>
          <tbody>
            <ng-container *ngFor="let config of configuracoes">
              <tr class="table-secondary configuration-header">
                <td colspan="5">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                      <button type="button"
                        class="btn btn-sm btn-link p-0 me-2 text-decoration-none expand-collapse-btn"
                        (click)="toggleConfiguracao(config)" [attr.aria-expanded]="isConfiguracaoExpanded(config)"
                        [attr.aria-label]="isConfiguracaoExpanded(config) ? 'Recolher regras' : 'Expandir regras'">
                        <i [class]="getExpandCollapseIcon(config)"></i>
                      </button>
                      <strong>{{ config.nome | uppercase }}</strong>
                      <span class="badge bg-secondary ms-2 rules-count-badge">{{ config.regras.length || 0 }}
                        regras</span>
                    </div>
                    <div>
                      <button (click)="carregarRegraParaEdicao(config)" class="btn btn-sm btn-primary me-2">
                        <i class="feather icon-edit"></i>
                        Editar configuração
                      </button>
                      <button (click)="excluirConfiguracao(config)" class="btn btn-sm btn-danger">
                        <i class="feather icon-trash-2 me-1"></i>
                        Excluir configuração
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
              <tr [style.display]="isConfiguracaoExpanded(config) ? 'table-row' : 'none'">
                <th></th>
                <th>Valor</th>
                <th>Operador</th>
                <th>Ativo</th>
                <th>Ações</th>
              </tr>
              <tr *ngFor="let regra of config.regras"
                [style.display]="isConfiguracaoExpanded(config) ? 'table-row' : 'none'">
                <td></td>
                <td>{{ regra.valor }}</td>
                <td>{{ getOperadorLabel(regra.operador) }}</td>
                <td>{{ regra.ativo ? 'Sim' : 'Não' }}</td>
                <td>
                  <button type="button" class="btn btn-sm btn-danger" (click)="excluirRegraClick(config, regra)">
                    <i class="feather icon-trash-2"></i>
                    Excluir Regra
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </app-card>
  </div>
</div>