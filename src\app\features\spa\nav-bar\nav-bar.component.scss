.b-brand {
  display: flex;
  align-items: center;
  text-decoration: none;

  .b-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    width: 40px;
    height: 40px;

    .brand-logo {
      width: 100%;
      height: auto;
      max-width: 35px;
      object-fit: contain;
      transition: transform 0.3s ease;

      @media (max-width: 991px) {
        transform: scale(0.9);
      }
    }
  }

  .b-title {
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
    transition: opacity 0.3s ease;
  }
}

.b-bg .b-logo{
  width: 8rem;
}

// Menu mobile
.mobile-menu {
  position: relative;
  display: inline-block;
  padding: 0.6rem;
  cursor: pointer;

  span {
    position: relative;
    display: block;
    width: 20px;
    height: 2px;
    transition: all 0.3s ease-in-out;

    &:before,
    &:after {
      position: absolute;
      content: '';
      width: 100%;
      height: 2px;
      transition: all 0.3s ease-in-out;
    }

    &:before {
      top: -6px;
    }

    &:after {
      bottom: -6px;
    }
  }

  &.on span {
    background: transparent;

    &:before {
      transform: rotate(45deg) translate(4px, 4px);
    }

    &:after {
      transform: rotate(-45deg) translate(4px, -4px);
    }
  }
}

// =================================================================
// Correções para Layout Mobile
// =================================================================
@media (max-width: 991px) {
  header {
    display: flex;
    align-items: center;
    justify-content: space-between; // Alinha itens nas extremidades
    padding: 0 15px;
    position: relative;
    width: 100%;
    height: 60px; // Altura padrão para header mobile

    .mobile-menu-posicao{
      position: absolute;
      left: 8px;
      top: 1rem;
    }
    // // Container da logo e menu hamburger
    // .m-header {
    //   display: flex !important; // Garantir que seja visível
    //   align-items: center;
    //   flex-grow: 1; // Ocupa o espaço disponível

    //   // Ícone do menu hamburger
    //   // .mobile-menu {
    //   //   position: static; // Remove o posicionamento absoluto
    //   //   order: 1; // Garante que venha primeiro
    //   //   padding: 8px;
    //   // }

    //   // Logo
    //   .b-brand {
    //     order: 2; // Fica no meio
    //     margin: 0 auto; // Centraliza horizontalmente
    //   }
    // }

    // // Ícone de "mais opções" (configurações)
    // #mobile-header {
    //   position: static; // Remove o posicionamento absoluto
    //   display: flex !important; // Garante que seja visível
    //   order: 3; // Garante que venha por último
    //   padding: 8px;
    // }

    // Oculta a navegação de desktop
    // .navbar-collapse {
    //   display: none !important;
    // }
  }
}

div.collapse.navbar-collapse.px-4{
  height: 2.5rem;
  margin-bottom: 0px;
}