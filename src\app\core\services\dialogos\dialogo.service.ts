import { inject, Injectable } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Observable, Subject, finalize, from, map, take, tap, takeUntil } from 'rxjs';
import { DialogoPerguntaComponent } from '../../../shared/components/dialogos/dialogo-pergunta/dialogo-pergunta.component';
import { DialogoUploadComponent } from '../../../shared/components/dialogos/dialogo-upload/dialogo-upload.component';
import { DadosDialogoNotificacao, DadosDialogoNotificacaoLista } from '../../../shared/interfaces/dialogos/dialogo-notificacao.interface';
import { DialogoNotificacaoComponent } from '../../../shared/components/dialogos/dialogo-notificacao/dialogo-notificacao.component';
import { DialogoNotificacaoListaComponent } from '../../../shared/components/dialogos/dialogo-notificacao-lista/dialogo-notificacao-lista.component';

@Injectable({
	providedIn: 'root',
})
export class DialogoService {
	private mostrandoNotificacao: Observable<any> | null = null;
	private mostrandoUpload: Observable<any> | null = null;

	private dialogosAbertos: NgbModalRef[] = [];
	
	private fecharTodosModais$ = new Subject<void>();

	public get mostrandoDialogo() {
		return this.mostrandoNotificacao 
			|| this.mostrandoUpload;
	}

	private readonly fechouDialogo = new Subject<void>();
	public readonly fechouDialogo$ = this.fechouDialogo.asObservable();

	//Injecao de dependencias
	ngbModal = inject(NgbModal);

	constructor() {}

	private adicionarDialogoAosDialogosAbertos(dialogo: NgbModalRef): void {
		(dialogo as any).id = Math.random().toString(36).substr(2, 9);
		this.dialogosAbertos.push(dialogo);
	}

	private removerDialogoDosDialogosAbertos(dialogo: NgbModalRef): void {
		const dialogoId = (dialogo as any).id;
		this.dialogosAbertos = this.dialogosAbertos.filter((d) => (d as any).id !== dialogoId);
		
	}

	dialogoNotificacao(dados: DadosDialogoNotificacao): Observable<any> {
		// if (this.mostrandoNotificacao) {
		// 	return EMPTY;
		// }

		const modalRef = this.ngbModal.open(DialogoNotificacaoComponent, {
			backdrop: 'static',
			keyboard: false,
			centered: true,
			windowClass: 'modal-base-custom',
		});

		modalRef.componentInstance.titulo = dados.titulo || 'Notificação';
		modalRef.componentInstance.descricao = dados.descricao || '';
		modalRef.componentInstance.corHeader = dados.corHeader || '';
		modalRef.componentInstance.mostrarBotaoCancelar = typeof dados.mostrarBotaoCancelar === 'boolean' ? dados.mostrarBotaoCancelar : true;
		
		if (dados.botaoConfirmar) {
			modalRef.componentInstance.botaoConfirmar = {
				...modalRef.componentInstance.botaoConfirmar,
				...dados.botaoConfirmar,
				dados: dados.dados,
			};
		}
		
		if (dados.botaoCancelar) {
			modalRef.componentInstance.botaoCancelar = {
				...modalRef.componentInstance.botaoCancelar,
				...dados.botaoCancelar,
			};
		}

		this.adicionarDialogoAosDialogosAbertos(modalRef);

		this.mostrandoNotificacao = from(modalRef.result).pipe(
			takeUntil(this.fecharTodosModais$),
			tap(() => this.removerDialogoDosDialogosAbertos(modalRef)),
			finalize(() => this.mostrandoNotificacao = null)
		);

		return this.mostrandoNotificacao;
	}

	dialogoNotificacaoLista(dados: DadosDialogoNotificacaoLista): Observable<any> {
		// if (this.mostrandoNotificacao) {
		// 	return EMPTY;
		// }

		const modalRef = this.ngbModal.open(DialogoNotificacaoListaComponent, {
			backdrop: 'static',
			keyboard: false,
			centered: true,
			windowClass: 'modal-base-custom'
		});

		modalRef.componentInstance.titulo = dados.titulo;
		modalRef.componentInstance.descricao = dados.descricao;
		modalRef.componentInstance.configCss = dados.configCss;
		modalRef.componentInstance.corHeader = dados.corHeader;
		modalRef.componentInstance.mostrarBotaoCancelar = dados.mostrarBotaoCancelar;
		
		if (dados.botaoConfirmar) {
			modalRef.componentInstance.botaoConfirmar = {
				...modalRef.componentInstance.botaoConfirmar,
				...dados.botaoConfirmar,
				dados: dados.dados,
			};
		}
		
		if (dados.mostrarBotaoCancelar && dados.botaoCancelar) {
			modalRef.componentInstance.botaoCancelar = {
				...modalRef.componentInstance.botaoCancelar,
				...dados.botaoCancelar,
			};
		}

		this.adicionarDialogoAosDialogosAbertos(modalRef);

		this.mostrandoNotificacao = from(modalRef.result).pipe(
			takeUntil(this.fecharTodosModais$),
			tap(() => this.removerDialogoDosDialogosAbertos(modalRef)),
			finalize(() => this.mostrandoNotificacao = null)
		);

		return this.mostrandoNotificacao;
	}

	dialogoPergunta(dados: DadosDialogoNotificacao): Observable<any> {
		// if (this.mostrandoNotificacao) {
		// 	return EMPTY;
		// }

		const modalRef = this.ngbModal.open(DialogoPerguntaComponent, {
			backdrop: 'static',
			keyboard: false,
			centered: true,
			windowClass: 'modal-base-custom',
		});

		modalRef.componentInstance.titulo = dados.titulo || 'Notificação';
		modalRef.componentInstance.descricao = dados.descricao || '';
		modalRef.componentInstance.corHeader = dados.corHeader || '';
		modalRef.componentInstance.mostrarBotaoCancelar = typeof dados.mostrarBotaoCancelar === 'boolean' ? dados.mostrarBotaoCancelar : true;
		
		if (dados.botaoConfirmar) {
			modalRef.componentInstance.botaoConfirmar = {
				...modalRef.componentInstance.botaoConfirmar,
				...dados.botaoConfirmar,
				dados: dados.dados,
			};
		}
		
		if (dados.mostrarBotaoCancelar && dados.botaoCancelar) {
			modalRef.componentInstance.botaoCancelar = {
				...modalRef.componentInstance.botaoCancelar,
				...dados.botaoCancelar,
			};
		}

		this.adicionarDialogoAosDialogosAbertos(modalRef);

		this.mostrandoNotificacao = from(modalRef.result).pipe(
			takeUntil(this.fecharTodosModais$),
			tap(() => this.removerDialogoDosDialogosAbertos(modalRef)),
			finalize(() => this.mostrandoNotificacao = null)
		);

		return this.mostrandoNotificacao;
	}

	upload(): Observable<File[]> {
		// if (this.mostrandoUpload) {
		// 	return EMPTY;
		// }

		const modalRef = this.ngbModal.open(DialogoUploadComponent, {
			backdrop: 'static',
			keyboard: false,
			centered: true,
			windowClass: 'modal-base-custom',
		});

		this.adicionarDialogoAosDialogosAbertos(modalRef);

		this.mostrandoUpload = from(modalRef.result).pipe(
			takeUntil(this.fecharTodosModais$),
			tap(() => this.removerDialogoDosDialogosAbertos(modalRef)),
			finalize(() => this.mostrandoNotificacao = null)
		);


		return this.mostrandoUpload;
	}


	fecharTodosModais(): void {
		this.dialogosAbertos.forEach(modal => {
			modal.close();
		});
		this.dialogosAbertos = [];
		this.fecharTodosModais$.next();
	}

}
