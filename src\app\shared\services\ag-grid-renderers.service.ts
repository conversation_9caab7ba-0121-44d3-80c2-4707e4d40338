import { Injectable } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

export interface CellRendererButtonParams extends ICellRendererParams {
  clicked: (data: any) => void;
}

@Injectable({
  providedIn: 'root'
})
export class AgGridRenderersService {
  
  // Factory para criar um renderizador de botão personalizado
  createButtonRenderer(iconClass: string, onClick: (params: any) => void, condition?: (params: any) => boolean): any {
    class ButtonRenderer implements ICellRendererAngularComp {
      private params: any;
      private buttonEl!: HTMLSpanElement;
      private containerEl!: HTMLDivElement;

      agInit(params: any): void {
        this.params = params;
        this.containerEl = document.createElement('div');
        this.containerEl.style.display = 'flex';
        this.containerEl.style.height = '100%';
        this.containerEl.style.placeContent = 'center';
        this.containerEl.style.paddingTop = '10px';
        
        this.buttonEl = document.createElement('span');
        this.buttonEl.style.cursor = 'pointer';
        this.buttonEl.style.fontSize = '16px';
        this.buttonEl.style.color = '#818181ff';
        
        if (condition && condition(params)) {
          this.buttonEl.className = iconClass;
          this.buttonEl.addEventListener('click', (e) => {
            e.stopPropagation();
            onClick(params);
          });
        } else if (!condition) {
          this.buttonEl.className = iconClass;
          this.buttonEl.addEventListener('click', (e) => {
            e.stopPropagation();
            onClick(params);
          });
        } else {
          this.buttonEl.className = 'feather icon-check text-success';
        }
        
        this.containerEl.appendChild(this.buttonEl);
      }

      refresh(params: any): boolean {
        return false;
      }

      getGui(): HTMLElement {
        return this.containerEl;
      }
    }

    return ButtonRenderer;
  }
}
