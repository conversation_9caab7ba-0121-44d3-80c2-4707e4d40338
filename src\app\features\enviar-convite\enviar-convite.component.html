<div class="card">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">Cadastrar novo Usuário</h5>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="conviteForm" (ngSubmit)="onSubmit()" autocomplete="off">
      <div class="mb-3">
        <label for="email" class="form-label">Enviar convite para:</label>
        <input
          type="email"
          id="email"
          class="form-control"
          formControlName="email"
          [ngClass]="{'is-invalid': enviado && conviteForm.get('email')?.invalid && enviado}"
          placeholder="Digite o e-mail"
        />
        <div *ngIf="enviado && conviteForm.get('email')?.errors" class="invalid-feedback">
          <span *ngIf="conviteForm.get('email')?.errors?.['required']">O e-mail é obrigatório.</span>
          <span *ngIf="conviteForm.get('email')?.errors?.['email']">Digite um e-mail válido.</span>
        </div>
      </div>
      <button type="submit" class="btn btn-primary">Enviar</button>
    </form>
  </div>
</div>
