import { Component, inject, output, input, signal, effect } from '@angular/core';
import { Location, NgClass } from '@angular/common';
import { NgScrollbarModule } from 'ngx-scrollbar';

import { NavigationItem, NavigationItems } from '../navigation';
import { NavGroupComponent } from './nav-group/nav-group.component';
import { environment } from '../../../../../environments/environment';
import { SelectSimplesComponent } from '../../../../shared/components/selects/select-simples/select-simples.component';
import { AuthService } from '../../../../core/auth/services/auth.service';
import { OpcaoSelect } from '../../../../shared/components/selects/select.enum';
import { GlobalService } from '../../../../core/services/global.service';

@Component({
	selector: 'app-nav-content',
	imports: [NavGroupComponent, NgScrollbarModule, NgClass, SelectSimplesComponent],
	templateUrl: './nav-content.component.html',
	styleUrls: ['./nav-content.component.scss'],
})
export class NavContentComponent {
	private location = inject(Location);
	private authService = inject(AuthService);
	private globalService = inject(GlobalService);

	cliente = signal<{ nome: string; valor: string | number }[]>([]);
	clienteSelecionado = signal<string | number>('');
	windowWidth = signal(window.innerWidth);

	currentApplicationVersion = environment.appVersion;
	menuCollapsed = input<boolean>();
	navigations!: NavigationItem[];
	NavCollapsedMob = output();

	constructor() {
		this.navigations = NavigationItems;

		const cliMock = [{nome_fantasia: 'Cliente Prisma', id: 1}];
		const cli = this.authService.getUsuarioAtual()?.clientes || cliMock;

		if (cli && cli.length > 0) {
			this.cliente.set(
				cli.map((cliente: any) => ({
					nome: cliente.nome_fantasia,
					valor: cliente.id
				}))
			);
			
			this.clienteSelecionado.set(this.cliente()[0].valor);
			this.globalService.setDadoCompartilhado(this.clienteSelecionado().toString());
		}

		effect(() => {
			this.globalService.setDadoCompartilhado(this.clienteSelecionado().toString());
		});
	}

	fireOutClick() {
		const current_url = this.location.path();
		const link = "a.nav-link[ href='" + current_url + "' ]";
		const ele = document.querySelector(link);
		if (ele !== null && ele !== undefined) {
			const parent = ele.parentElement;
			if (parent) {
				const up_parent = parent.parentElement?.parentElement;
				const last_parent = up_parent?.parentElement;
				if (parent.classList.contains('pcoded-hasmenu')) {
					parent.classList.add('pcoded-trigger');
					parent.classList.add('active');
				} else if (up_parent?.classList.contains('pcoded-hasmenu')) {
					up_parent.classList.add('pcoded-trigger');
					up_parent.classList.add('active');
				} else if (last_parent?.classList.contains('pcoded-hasmenu')) {
					last_parent.classList.add('pcoded-trigger');
					last_parent.classList.add('active');
				}
			}
		}
	}

	onClienteChange(event: string | number) {
		this.clienteSelecionado.set(event);
	}
}
