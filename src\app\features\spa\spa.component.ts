import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

import { NavBarComponent } from './nav-bar/nav-bar.component';
import { BreadcrumbsComponent } from '../../shared/components/breadcrumbs/breadcrumbs.component';
import { NavigationComponent } from './navigation/navigation.component';

@Component({
  selector: 'app-spa',
  imports: [NavBarComponent, NavigationComponent, RouterModule, CommonModule, BreadcrumbsComponent],
  templateUrl: './spa.component.html',
  styleUrls: ['./spa.component.scss'],
})
export class SpaComponent {
  navCollapsed: boolean | undefined;
  navCollapsedMob: boolean;
  windowWidth: number;

  constructor() {
    this.windowWidth = window.innerWidth;
    this.navCollapsedMob = false;
  }

  navMobClick() {
    const navbar = document.querySelector('app-navigation.pcoded-navbar');
    if (this.navCollapsedMob && navbar && !navbar.classList.contains('mob-open')) {
      this.navCollapsedMob = !this.navCollapsedMob;
      setTimeout(() => {
        this.navCollapsedMob = !this.navCollapsedMob;
      }, 100);
    } else {
      this.navCollapsedMob = !this.navCollapsedMob;
    }
  }

  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.closeMenu();
    }
  }

  closeMenu() {
    const navbar = document.querySelector('app-navigation.pcoded-navbar');
    if (navbar && navbar.classList.contains('mob-open')) {
      navbar.classList.remove('mob-open');
    }
  }
}
