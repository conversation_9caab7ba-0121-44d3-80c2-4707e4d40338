import { Injectable, signal, Signal, WritableSignal } from '@angular/core';
import { Observable, of } from 'rxjs';
import painelMock from '../../shared/mocks/painel-principal.json';
import detalhesMock from '../../shared/mocks/movimentacao-detalhes.json';
import movimentacoesMock from '../../shared/mocks/movimentacoes.json';
import { Movimentacao } from '../interfaces/api/movimentacao.interface';
import { MovimentacoesDetalhes } from '../interfaces/api/movimentacao-detalhes.interface';

@Injectable({
	providedIn: 'root',
})
export class MockDataService {
	mockMovimentacoes: WritableSignal<Movimentacao[]> = signal<Movimentacao[]>([]);
	mockDetalhesMovimentacoes: WritableSignal<any[]> = signal<any[]>([]);
	mockPainelPrincipal: WritableSignal<any> = signal<any>({});

	constructor() {
		// Inicializa os mocks com dados vazios
		this.mockMovimentacoes.set(movimentacoesMock);
		this.mockPainelPrincipal.set(painelMock);
	}

	atualizaStatusVeiculo(idAgregador: string, idAtivo: string, novoStatus: { valor: number; descricao: string }): Observable<any> {
		const movimentacaoDetalhes = this.mockDetalhesMovimentacoes().find((item: any) => item.idMovimentacao.toString() === idAgregador);
		if (movimentacaoDetalhes) {
			const ativo = movimentacaoDetalhes.ativos?.find((a: any) => a.id.toString() === idAtivo);
			if (ativo) {
				ativo.status = novoStatus.descricao;
			}
		}

		const movimentacao = this.mockMovimentacoes().find((item: any) => item.idMovimentacao.toString() === idAgregador);
		if (movimentacao) {
			movimentacao.ativoStatus = novoStatus;
		}

		return of(null);
	}

	atualizarRessarcimentoService(idMovimentacao: string, valorPago: number): Observable<any> {
		// Atualiza em mockMovimentacoes
		const movimentacao = this.mockMovimentacoes().find((item: any) => item.idMovimentacao.toString() === idMovimentacao);
		if (movimentacao) {
			movimentacao.valorRessarcido = movimentacao.valorRessarcido + valorPago;
			movimentacao.valorRessarcir = movimentacao.valorRessarcir - valorPago;
		}

		// Atualiza em mockDetalhesMovimentacoes
		const detalhe = this.mockDetalhesMovimentacoes().find((item: any) => item.idMovimentacao.toString() === idMovimentacao);
		if (detalhe) {
			detalhe.valoresResumo.ressarcido = detalhe.valoresResumo.ressarcido + valorPago;
			detalhe.valoresResumo.ressarcir = detalhe.valoresResumo.ressarcir - valorPago;
		}

		// Atualiza mockPainelPrincipal
		const painel = this.mockPainelPrincipal();
		if (painel.ressarcimento) {
			painel.ressarcimento.pendentes.valor = Math.max(0, painel.ressarcimento.pendentes.valor - valorPago);
			painel.ressarcimento.realizados.valor += valorPago;

			// Recalcula as porcentagens
			const totalRessarcimento = painel.ressarcimento.valor;
			if (totalRessarcimento > 0) {
				painel.ressarcimento.realizados.porcentagem = (painel.ressarcimento.realizados.valor / totalRessarcimento) * 100;
				painel.ressarcimento.pendentes.porcentagem = (painel.ressarcimento.pendentes.valor / totalRessarcimento) * 100;
			}
			this.mockPainelPrincipal.set(painel);
		}

		return of(null);
	}
}
