import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { ClienteEmpresa, ClienteEmpresaResponse } from '../../../shared/interfaces/api/cliente-empresa.interface';

@Injectable({
  providedIn: 'root'
})
export class ClienteEmpresaService {
  private readonly API_BASE = environment.apiUrlAuth;

  constructor(private http: HttpClient) {}

  criar(clienteEmpresa: ClienteEmpresa): Observable<ClienteEmpresaResponse> {
    return this.http.post<ClienteEmpresaResponse>(`${this.API_BASE}/cliente-empresas`, clienteEmpresa);
  }
}
