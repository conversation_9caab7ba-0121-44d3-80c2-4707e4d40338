<div class="account-pages">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-md-8 col-lg-6 col-xl-6">
				<div class="card bg-pattern">
					<div class="card-body p-4">
						<div class="text-center w-75 m-auto">
							<!-- <div class="auth-brand">
								<a href="#" class="logo logo-light text-center">
									<span class="logo-lg">
										<img src="assets/images/logo.svg" alt="" height="22" />
									</span>
								</a>
							</div> -->
              
							@if (!erroEnvioEmail()) {
								<p class="text-muted mb-4 mt-3">
									{{mensagemEnvioEmail()}}
								</p>
							}
                            @if (erroEnvioEmail()) {
                                <!-- Informações do email -->
                                <div class="text-center mb-3">
                                    <small class="text-muted">
                                        {{mensagemEnvioEmail()}}
                                    </small>
                                </div>

                                <div class="alert alert-danger mt-4" role="alert">
                                    <i class="mdi mdi-alert-circle-outline me-2"></i>
                                    Valide seu e-mail ou tente reenviar o código.
                                </div>
                            }
						</div>

						<form [formGroup]="doisFatForm" (ngSubmit)="onSubmit()">
							@if (!erroEnvioEmail()) {
								<div class="mb-3">
									<label for="codigo" class="form-label">Código de verificação:</label>
									<input
										type="text"
										class="form-control"
										id="codigo"
										formControlName="codigo"
										placeholder="000000"
										maxlength="6"
										[class.is-invalid]="doisFatForm.get('codigo')?.invalid && doisFatForm.get('codigo')?.touched"
										autocomplete="off"
										inputmode="numeric"
										pattern="[0-9]*"
									/>
									<div class="invalid-feedback" *ngIf="doisFatForm.get('codigo')?.invalid && doisFatForm.get('codigo')?.touched">
										<div *ngIf="doisFatForm.get('codigo')?.errors?.['required']">Código é obrigatório</div>
										<div *ngIf="doisFatForm.get('codigo')?.errors?.['pattern']">Código deve conter 6 dígitos</div>
									</div>
								</div>
							}

                            @if (erroVerificaCodigo() && mensagemVerificaCodigo()) {
                                <div class="alert alert-danger" role="alert">
                                    <i class="mdi mdi-alert-circle-outline me-2"></i>
                                    {{ mensagemVerificaCodigo() }}
                                </div>
                            }

							
                            @if (!erroVerificaCodigo() && mensagemVerificaCodigo()) {
                                <div class="alert alert-success" role="alert">
                                    <i class="mdi mdi-check-circle-outline me-2"></i>
                                    {{ mensagemVerificaCodigo() }}
                                </div>
                            }
                            
                            @if (!erroEnvioEmail()) {
                                <!-- Botão de verificar -->
                                <div class="text-center d-grid">
                                    <button class="btn btn-primary" type="submit" [disabled]="!doisFatForm.valid || carregando()">
                                        <span *ngIf="carregando()" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                        <span *ngIf="carregando()">Verificando...</span>
                                        <span *ngIf="!carregando()">Verificar código</span>
                                    </button>
                                </div>
                            }
						</form>

					

					</div>
				</div>
			</div>
		</div>
	</div>
</div>
