import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../auth/services/auth.service';
import { Movimentacao } from '../../../shared/interfaces/api/movimentacao.interface';
import { MockDataService } from '../../../shared/services/mock-data.service';
import { PainelPrincipal } from '../../../shared/interfaces/api/painel-principal.interface';

@Injectable({
  providedIn: 'root'
})
export class PaginaInicialService {
  private readonly API_BASE = environment.useProxy ? '/api-cors/painel-principal' : `${environment.apiUrl}/painel-principal`;
  // private readonly API_BASE = `http://localhost:5010/dashboard/cards` // Usa em desenvolvimento para API local
  private readonly usaMock: boolean = environment.useMock;

  // Injeções de dependências
  private mockDataService = inject(MockDataService);
  private http = inject(HttpClient);
  private authService = inject(AuthService);

  constructor() {}

  private buildHeaders(tenantId: string): HttpHeaders {
    const token = this.authService.getToken();
    let headers = new HttpHeaders({ 'x-tenant-id': tenantId });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }
    return headers;

  }

  todos(tenantId: string): Observable<PainelPrincipal> {

    if(this.usaMock) {
      return of(this.mockDataService.mockPainelPrincipal());
    }

    const headers = this.buildHeaders(tenantId);
    return this.http.get<PainelPrincipal>(`${this.API_BASE}`, { headers });
  }

  
}
