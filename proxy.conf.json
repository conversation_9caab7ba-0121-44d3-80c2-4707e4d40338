{"/api-cors/auth/*": {"target": "https://zinidentity.sistemaprismatec.com.br:8443/auth", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api-cors/auth": ""}, "headers": {"Accept": "application/json", "Content-Type": "application/json", "Origin": "https://zinidentity.sistemaprismatec.com.br:8443"}}, "/api-cors/*": {"target": "https://zinwebapi.sistemaprismatec.com.br:8443", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api-cors": ""}, "headers": {"Accept": "application/json", "Content-Type": "application/json", "Origin": "https://zinwebapi.sistemaprismatec.com.br:8443"}}}