import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../core/auth/services/auth.service';
import { CadastroNovoUsuarioRequest } from '../../../shared/interfaces/auth/requests';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';

@Component({
  selector: 'app-cadastro-novo-usuario',
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './cadastro-novo-usuario.component.html',
  styleUrls: ['./cadastro-novo-usuario.component.scss']
})
export class CadastroNovoUsuarioComponent implements OnInit {
  formulario: FormGroup;
  usuario: string = '';
  token: string = '';
  enviado: boolean = false;
  hideNewPassword = true;
  hideConfirmPassword = true;
  isLoading = false;
  errorMessage = '';

  private fb = inject(FormBuilder);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private authService = inject(AuthService);
  private dialogoService = inject(DialogoService);

  constructor() {
    this.formulario = this.fb.group({
      newUsername: ['', Validators.required],
      newPassword: ['', [
        Validators.required,
        Validators.minLength(6),
        Validators.pattern(/^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).+$/)
      ]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.senhasIguaisValidator });
  }

  senhasIguaisValidator: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    const senha = group.get('newPassword')?.value;
    const confirma = group.get('confirmPassword')?.value;
    if (senha && confirma && senha !== confirma) {
      return { senhasDiferentes: true };
    }
    return null;
  };

  ngOnInit(): void {
    this.usuario = this.route.snapshot.queryParamMap.get('usuario') || '';
    const tokenEncoded = this.route.snapshot.queryParamMap.get('token') || '';
    this.token = decodeURIComponent(tokenEncoded);
  }

  enviar(): void {
    if (this.formulario.valid) {
      const dadosUsuario: CadastroNovoUsuarioRequest = {
        username: this.usuario,
        newUsername: this.formulario.value.newUsername,
        token: this.token,
        newPassword: this.formulario.value.newPassword
      };

      this.authService.cadastroNovoUsuario(dadosUsuario).subscribe({
        next: (response) => {
            this.enviado = true;
            this.router.navigate(['/pagina-inicial']);
            },
        error: (error) => { 
            this.enviado = true;
            console.error('Erro ao cadastrar novo usuário:', error);
            this.dialogoService.dialogoNotificacao({
              titulo: 'Erro ao cadastrar novo usuário',
              descricao: error.error.result.message || 'Ocorreu um erro ao cadastrar o usuário.',
              corHeader: 'danger',
              mostrarBotaoCancelar: false,
              botaoConfirmar: {
                texto: 'OK',
                classe: 'btn-danger',
              }
            }).subscribe();
        }
      });

    }
  }
}
