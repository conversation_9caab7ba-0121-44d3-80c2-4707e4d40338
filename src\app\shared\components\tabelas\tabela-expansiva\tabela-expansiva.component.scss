@import 'src/scss/_variables.scss';

/* Estilos para o container da tabela */
.tabela-container {
	width: 100%;
	overflow-x: auto;
}

/* Estilos gerais para a tabela */
.tabela-fixa {
	min-width: 200px; /* <PERSON>arante que a tabela tenha uma largura mínima */
}

.tabela-destaque tbody tr:hover {
	background-color: #f5f5f5; /* Cor de destaque ao passar o mouse */
}

/* Cabeçalho da tabela principal */
.header-tabela {
	font-size: 12px;
	background: #3f4d67d0 !important;
}

table {
	thead {
		th {
			color: #fff !important;
			white-space: pre-line !important;
			align-content: center !important;
		}
	}
}

/* Linha expandida */
.linha-expandida {
	background-color: #f5f5f5 !important; /* Cor de fundo para a linha que está expandida */
}

.header-tabela-colapsada {
	background: #3f4d6794 !important;
}

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

.text-success {
	color: #28a745 !important;
}

.text-danger {
	color: #dc3545 !important;
}

.text-warning {
	color: #ffc107 !important;
}
