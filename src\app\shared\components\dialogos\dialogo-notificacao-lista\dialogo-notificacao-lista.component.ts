import { Component, inject, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { BotaoConfig } from '../../../interfaces/dialogos/botao-config.interface';
import { ResultadoDialogo } from '../../../interfaces/dialogos/resultado-dialogo.interface';
import { ConfiguracaoDialogoLista } from '../../../utils/dialogo-descricao-lista.util';
import { TipoCorHeader } from '../../../enums/dialogos/tipo-cor-header.enum';

@Component({
	selector: 'app-dialogo-notificacao-lista',
	standalone: true,
	imports: [CommonModule],
	templateUrl: './dialogo-notificacao-lista.component.html',
	styleUrls: ['./dialogo-notificacao-lista.component.scss'],
})
export class DialogoNotificacaoListaComponent {
	private activeModal = inject(NgbActiveModal);

	@Input() titulo: string = 'Confirmação';
	@Input() descricao: string[] = ['Deseja confirmar esta ação?'];
	@Input() mostrarBotaoCancelar: boolean = false;
	@Input() botaoConfirmar: BotaoConfig = {
		texto: 'Sim',
		habilitaIcone: true,
		classe: 'btn-primary',
		icone: 'feather icon-check',
	};
	@Input() botaoCancelar: BotaoConfig = {
		texto: 'Não',
		habilitaIcone: true,
		classe: 'btn-outline-dark',
		icone: 'feather icon-x',
	};

	@Input() configCss?: Partial<ConfiguracaoDialogoLista> | null = null;
	@Input() corHeader: TipoCorHeader = TipoCorHeader.info;

	configListCss: Partial<ConfiguracaoDialogoLista> = {
		icone: 'feather icon-alert-triangle',
		corIcone: 'text-warning'
	};

	constructor() {}

	ngOnInit(): void {
		if (this.configCss) {
			this.configListCss = this.configCss;
		}
  	}

	onClick(click: boolean): void {
		const resultado: ResultadoDialogo = {
			confirmado: click,
			dados: null,
		};
		this.activeModal.close(resultado);
	}
}
