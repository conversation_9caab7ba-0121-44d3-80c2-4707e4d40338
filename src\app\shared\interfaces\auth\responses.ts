export interface LoginResponse {
  result: {
    token: string;
    refreshToken: string;
    expiration: Date;
    message?: string;
    twoFactorEnabled?: boolean; 
  },
  statusCode: number;
  success: boolean;
}

export interface CadastrarResponse {
  user: {
    username: string;
    email: string;
  };
}

export interface RecuperarSenhaResponse {
  resetCode: "string",
}

export interface ConfirmacaoEmailResponse {
  result: {
    expiration: Date;
    message: string | null;
    refreshToken: string
    token: string
    twoFactorEnabled:boolean;  
  };

  statusCode: number;
  success: boolean;
}

export interface verificaCodigoEmailResponse extends LoginResponse {
  
}
