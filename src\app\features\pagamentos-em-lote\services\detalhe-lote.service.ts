import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';
import { DetalheLote, LinhasProcessadasResponse, FiltrosDetalheLote } from '../interfaces/detalhe-lote.interface';
import { StatusLoteImportacao } from '../../../shared/enums/status-lote-importacao.enum';
import { StatusLinhaPagamento } from '../../../shared/enums/status-linha-pagamento.enum';
import { OperacaoLinhaPagamento } from '../../../shared/enums/operacao-linha-pagamento.enum';

@Injectable({
  providedIn: 'root'
})
export class DetalheLoteService {

  constructor() { }

  obterDetalheLote(loteId: number): Observable<DetalheLote> {
    const mockData: DetalheLote = {
      id: loteId,
      nomeArquivo: "pagamentos_janeiro_2024.xlsx",
      dataProcessamento: "2024-01-15T09:30:00Z",
      usuarioProcessamento: "<EMAIL>",
      status: StatusLoteImportacao.ConcluidoComErros,
      totalLinhasLidas: 100,
      totalProgramados: 60,
      totalLiquidados: 35,
      totalCriados: 45,
      totalIgnorados: 3,
      totalDuplicados: 2,
      totalErros: 5,
      totalProcessados: 95,
      totalComProblemas: 10,
      temErros: true,
      temIgnorados: true,
      tipoArquivo: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      tamanhoArquivo: 2048576,
      dataInicio: "2024-01-15T09:30:00Z",
      dataFim: "2024-01-15T09:35:30Z",
      tempoProcessamento: "00:05:30"
    };

    return of(mockData).pipe(delay(500));
  }

  obterLinhasProcessadas(
    loteId: number, 
    filtros: FiltrosDetalheLote, 
    page: number = 1, 
    pageSize: number = 20
  ): Observable<LinhasProcessadasResponse> {
    
    // Mock data for processed lines
    const allLines = this.generateMockLines(loteId);
    
    // Apply filters
    let filteredLines = allLines;
    
    if (filtros.statusLinha) {
      filteredLines = filteredLines.filter(linha => linha.status === filtros.statusLinha);
    }
    
    if (filtros.operacaoAplicada) {
      filteredLines = filteredLines.filter(linha => linha.operacaoAplicada === filtros.operacaoAplicada);
    }
    
    if (filtros.numeroNF) {
      filteredLines = filteredLines.filter(linha => 
        linha.numeroNF.toLowerCase().includes(filtros.numeroNF.toLowerCase())
      );
    }
    
    if (filtros.cnpjs) {
      filteredLines = filteredLines.filter(linha => 
        linha.cnpjPagador.includes(filtros.cnpjs) || 
        linha.cnpjCpfFavorecido.includes(filtros.cnpjs)
      );
    }

    // Pagination
    const totalItems = filteredLines.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedLines = filteredLines.slice(startIndex, endIndex);

    const response: LinhasProcessadasResponse = {
      linhas: paginatedLines,
      loteInfo: {
        id: loteId,
        nomeArquivo: "pagamentos_janeiro_2024.xlsx",
        dataProcessamento: "2024-01-15T09:30:00Z",
        status: StatusLoteImportacao.ConcluidoComErros,
        totalLinhasLidas: 100,
        totalErros: 5
      },
      totalItems,
      currentPage: page,
      pageSize,
      totalPages
    };

    return of(response).pipe(delay(300));
  }

  baixarArquivoProcessado(loteId: number): Observable<Blob> {
    // Mock file download
    const mockContent = 'Mock Excel file content for lote ' + loteId;
    const blob = new Blob([mockContent], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    
    return of(blob).pipe(delay(1000));
  }

  reprocessarLinha(loteId: number, linhaId: number): Observable<boolean> {
    // Mock reprocess line
    return of(true).pipe(delay(1500));
  }

  private generateMockLines(loteId: number) {
    const lines = [];
    const operacoes = [
      OperacaoLinhaPagamento.Programar,
      OperacaoLinhaPagamento.Liquidar,
      OperacaoLinhaPagamento.Ignorado,
      OperacaoLinhaPagamento.Duplicado,
      OperacaoLinhaPagamento.Erro
    ];
    
    const statuses = [StatusLinhaPagamento.OK, StatusLinhaPagamento.Erro];
    
    for (let i = 1; i <= 50; i++) {
      const operacao = operacoes[Math.floor(Math.random() * operacoes.length)];
      const status = operacao === OperacaoLinhaPagamento.Erro ? 
        StatusLinhaPagamento.Erro : 
        statuses[Math.floor(Math.random() * statuses.length)];
      
      lines.push({
        id: i,
        numeroLinha: i + 1,
        cnpjPagador: `1122233300014${i.toString().padStart(2, '0')}`,
        cnpjCpfFavorecido: `1234567800010${i.toString().padStart(2, '0')}`,
        numeroNF: `NF-${i.toString().padStart(3, '0')}`,
        dataProgramacao: i % 3 === 0 ? "2024-01-20T00:00:00Z" : null,
        dataLiquidacao: i % 2 === 0 ? "2024-01-15T10:00:00Z" : null,
        valorPago: Math.round((Math.random() * 5000 + 100) * 100) / 100,
        operacaoAplicada: operacao,
        status: status,
        mensagem: this.getMockMessage(operacao, status),
        idPagamento: status === StatusLinhaPagamento.OK ? 400 + i : null,
        pagamentoEncontrado: Math.random() > 0.3,
        pagamentoCriado: Math.random() > 0.7
      });
    }
    
    return lines;
  }

  private getMockMessage(operacao: OperacaoLinhaPagamento, status: StatusLinhaPagamento): string {
    if (status === StatusLinhaPagamento.Erro) {
      const errorMessages = [
        'CNPJ do favorecido não encontrado no sistema',
        'Valor do pagamento divergente da NF',
        'Data de programação inválida',
        'Duplicata já processada anteriormente',
        'Formato de dados inválido na linha'
      ];
      return errorMessages[Math.floor(Math.random() * errorMessages.length)];
    }
    
    switch (operacao) {
      case OperacaoLinhaPagamento.Programar:
        return 'Pagamento programado com sucesso';
      case OperacaoLinhaPagamento.Liquidar:
        return 'Pagamento liquidado com sucesso';
      case OperacaoLinhaPagamento.Ignorado:
        return 'Linha ignorada - dados já processados';
      case OperacaoLinhaPagamento.Duplicado:
        return 'Linha duplicada - registro já existe';
      default:
        return 'Processado com sucesso';
    }
  }
}
