<div class="auth-wrapper">
  <div class="auth-content">
    <div class="card">
      <div class="card-body text-center">
        <div class="mb-4">
          <img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
        </div>
        <h3 class="mb-4"><PERSON><PERSON><PERSON></h3>
        <form [formGroup]="formulario" (ngSubmit)="enviar()">
          <div class="input-group mb-3">
            <input
              type="text"
              class="form-control"
              placeholder="Novo Usuário"
              formControlName="newUsername"
              [class.is-invalid]="formulario.get('newUsername')?.invalid && formulario.get('newUsername')?.touched"
            />
            <div class="invalid-feedback" *ngIf="formulario.get('newUsername')?.hasError('required') && formulario.get('newUsername')?.touched">
              Usuário é obrigatório
            </div>
          </div>

          <div class="input-group mb-4">
            <input
              [type]="hideNewPassword ? 'password' : 'text'"
              class="form-control"
              placeholder="Nova senha"
              formControlName="newPassword"
              [class.is-invalid]="(formulario.get('newPassword')?.invalid && formulario.get('newPassword')?.touched) || formulario.hasError('senhasDiferentes')"
            />
            <button class="btn btn-outline-secondary" type="button" (click)="hideNewPassword = !hideNewPassword">
              <i [class]="hideNewPassword ? 'feather icon-eye-off' : 'feather icon-eye'"></i>
            </button>
            <div class="invalid-feedback" *ngIf="formulario.get('newPassword')?.hasError('required') && formulario.get('newPassword')?.touched">
              Senha é obrigatória
            </div>
            <div class="invalid-feedback" *ngIf="formulario.get('newPassword')?.hasError('minlength') && formulario.get('newPassword')?.touched">
              Senha deve ter pelo menos 6 caracteres
            </div>
            <div class="invalid-feedback" *ngIf="formulario.get('newPassword')?.hasError('pattern') && formulario.get('newPassword')?.touched">
              Senha deve conter pelo menos 1 caractere especial e 1 letra maiúscula
            </div>
          </div>

          <div class="input-group mb-4">
            <input
              [type]="hideConfirmPassword ? 'password' : 'text'"
              class="form-control"
              placeholder="Confirme a nova senha"
              formControlName="confirmPassword"
              [class.is-invalid]="(formulario.get('confirmPassword')?.invalid && formulario.get('confirmPassword')?.touched) || formulario.hasError('senhasDiferentes')"
            />
            <button class="btn btn-outline-secondary" type="button" (click)="hideConfirmPassword = !hideConfirmPassword">
              <i [class]="hideConfirmPassword ? 'feather icon-eye-off' : 'feather icon-eye'"></i>
            </button>
            <div class="invalid-feedback" *ngIf="formulario.get('confirmPassword')?.hasError('required') && formulario.get('confirmPassword')?.touched">
              Confirmação de senha é obrigatória
            </div>
            <div class="invalid-feedback" *ngIf="formulario.hasError('senhasDiferentes') && (formulario.get('confirmPassword')?.touched || formulario.get('newPassword')?.touched)">
              As senhas não coincidem
            </div>
          </div>

          <div class="alert alert-danger" *ngIf="errorMessage">
            <i class="feather icon-alert-circle"></i>
            {{ errorMessage }}
          </div>

          <button type="submit" class="btn btn-primary mb-4" [disabled]="formulario.invalid || isLoading">
            <span *ngIf="!isLoading">Cadastrar</span>
            <span *ngIf="isLoading">
              <span class="spinner-border spinner-border-sm me-2" role="status"></span>
              Cadastrando...
            </span>
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
