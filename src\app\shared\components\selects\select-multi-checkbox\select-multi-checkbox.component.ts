import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { OpcaoSelect } from '../select.enum';


@Component({
	selector: 'app-select-multi-checkbox',
	standalone: true,
	imports: [CommonModule, FormsModule],
	templateUrl: './select-multi-checkbox.component.html',
	styleUrl: './select-multi-checkbox.component.scss',
})
export class SelectMultiCheckboxComponent {
	@Input() opcoes: OpcaoSelect[] = [];
	@Input() selecionados: string[] = [];
	@Input() placeholder: string = 'Selecione';
	@Input() label: string = '';
	@Output() alteracaoSelecao = new EventEmitter<string[]>();
	aberto: boolean = false;
	termoBusca: string = '';

	get opcoesFiltradas(): OpcaoSelect[] {
		if (!this.termoBusca) return this.opcoes;
		return this.opcoes.filter((opcao) => opcao.nome.toLowerCase().includes(this.termoBusca.toLowerCase()));
	}

	alternarDropdown(): void {
		this.aberto = !this.aberto;
	}

	estaSelecionado(valor: any): boolean {
		return this.selecionados.includes(valor);
	}

	alternarSelecao(valor: any): void {
		if (this.estaSelecionado(valor)) {
			this.selecionados = this.selecionados.filter((v) => v !== valor);
		} else {
			this.selecionados = [...this.selecionados, valor];
		}
		this.alteracaoSelecao.emit(this.selecionados);
	}

	getNomeOpcao(valor: any): string {
		const opcao = this.opcoes?.find((o: any) => o.valor === valor);
		return opcao ? opcao.nome : valor;
	}

	selecionarTodos(): void {
		if (this.todosSelecionados()) {
			this.selecionados = [];
		} else {
			// this.selecionados = this.opcoesFiltradas.map((o) => o.valor);
		}
		this.alteracaoSelecao.emit(this.selecionados);
	}

	todosSelecionados(): boolean {
		return this.opcoesFiltradas.length > 0 && this.selecionados.length === this.opcoesFiltradas.length;
	}

	limparSelecao(): void {
		this.selecionados = [];
		this.alteracaoSelecao.emit(this.selecionados);
	}
}
