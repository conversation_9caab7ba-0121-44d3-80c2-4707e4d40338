<div class="auth-wrapper">
	<div class="auth-content">
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					<img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
				</div>
				<h3 class="mb-4">Atualizar Status do Veículo</h3>
				<form [formGroup]="formulario" (ngSubmit)="enviar()">
					<div class="input-group mb-4">
						<select class="form-select" formControlName="status">
							<option *ngFor="let status of statusOptions" [value]="status.valor">
								{{ status.descricao }}
							</option>
						</select>
					</div>
					<div class="alert alert-danger" *ngIf="errorMessage">
						<i class="feather icon-alert-circle"></i>
						{{ errorMessage }}
					</div>

					<button class="btn btn-primary mb-4" [disabled]="formulario.invalid || isLoading()">
						<span *ngIf="!isLoading()">Enviar</span>
						<span *ngIf="isLoading()">
							<span class="spinner-border spinner-border-sm me-2" role="status"></span>
							Carregando...
						</span>
					</button>
				</form>
			</div>
		</div>
	</div>
</div>
