import { ChangeDetectorRef, Component, inject, signal } from '@angular/core';
import { TabelaPadraoComponent } from "../../../../shared/components/tabelas/tabela-padrao/tabela-padrao.component";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Ressarcimento } from '../../../../shared/interfaces/api/ressarcimento.interface';
import { DialogoService } from '../../../../core/services/dialogos/dialogo.service';
import { ColDef } from 'ag-grid-community';
import { Router } from '@angular/router';
import { TipoDialogoDescList } from '../../../../shared/enums/dialogos/tipo-dialogo-descricao-list.enum';
import { ConfiguracaoDialogoLista, DialogoDescricaoListaUtil } from '../../../../shared/utils/dialogo-descricao-lista.util';
import { RessarcimentoService } from '../../../../core/services/api/ressarcimento.service';
import { StatusReparo } from '../../../../shared/enums/status-reparo.enum';

@Component({
  selector: 'app-ressarcimentos-cobrados',
  imports: [TabelaPadraoComponent, CommonModule, FormsModule],
  templateUrl: './ressarcimentos-cobrados.component.html',
  styleUrl: './ressarcimentos-cobrados.component.scss'
})
export class RessarcimentosCobradosComponent {
	erro = signal('');

	statusReparoEnum = Object.values(StatusReparo).map((valor) => ({ valor }));

	

	showGrid: boolean = true;
	
	numeroSinistro: string = '';
	fornecedor: string = '';
	dataAutorizacaoInicial: string = '';
	dataAutorizacaoFinal: string = '';
	numeroDocumento: string = '';
	statusReparo: string = '';
	placa: string = '';
	divergencias: boolean = false;
	condicoes: boolean = false;

	ngOnInit() {
		console.log(this.statusReparoEnum);
		this.ressarcimentosService.listarRessarcimentosCobrados(this.numeroSinistro, this.fornecedor, this.dataAutorizacaoInicial, this.dataAutorizacaoFinal, this.numeroDocumento, this.statusReparo, this.placa, this.divergencias, this.condicoes).subscribe({
			next: (dados: Ressarcimento[]) => {
				this.dadosTabela.set(dados);
			},
			error: (err) => {
				this.erro.set('Erro ao carregar dados');
			}
		});
	}

	private ressarcimentosService = inject(RessarcimentoService);

	dadosRessarcimentos = signal<Ressarcimento[]>([]);

	public filtrosApi: { label: string; valor: string | string[]; tipo?: 'text' | 'combo'; opcoes?: string[] }[] = [
		{ label: 'Nº Sinistro', valor: '', tipo: 'text' },
		{ label: 'Fornecedor', valor: '', tipo: 'text' },
		{ label: 'Data Autorização Inicial', valor: '', tipo: 'text' },
		{ label: 'Data Autorização Final', valor: '', tipo: 'text' },
		{ label: 'NF', valor: '', tipo: 'text' },
		{ label: 'Status Reparo', valor: '', tipo: 'combo', opcoes: Object.values(StatusReparo) },
		{ label: 'Placa', valor: '', tipo: 'text' },
		{ label: 'Divergência', valor: '', tipo: 'combo', opcoes: ['Sim', 'Não'] },
		{ label: 'Condição', valor: '', tipo: 'combo', opcoes: ['Boa', 'Ruim'] },
	];

	getFiltrosParaApi() {
		const filtros: any = {};
		this.filtrosApi.forEach(filtro => {
			if (filtro.valor) {
				filtros[filtro.label] = filtro.valor;
			}
		});
		return filtros;
	}

	public columnDefs: ColDef[] = [
		{ field: 'agregador', headerName: 'Nº Sinistro', minWidth: 120 },
		{ field: 'fornecedor', headerName: 'Fornecedor', minWidth: 120 },
		{
			field: 'dataAutorizacao',
			headerName: 'Data Autorização',
			minWidth: 120,
			valueFormatter: (params: any) => {
				if (!params.value) return '';
				const date = new Date(params.value);
				return date.toLocaleDateString('pt-BR');
			}
		},
		{ field: 'numeroDocumento', headerName: 'NF', minWidth: 120 },
		{ field: 'valorTotal', headerName: 'Total', minWidth: 120, valueFormatter: (params: any) => params.value != null ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '' },
		{ field: 'ressarcir', headerName: 'Ressarcir', minWidth: 120, valueFormatter: (params: any) => params.value != null ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '' },
		{ field: 'ressarcido', headerName: 'Ressarcido', minWidth: 120, valueFormatter: (params: any) => params.value != null ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '' },
		{ field: 'placa', headerName: 'Placa', minWidth: 120 },
		{ field: 'statusReparo', headerName: 'Status Reparo', minWidth: 120 },
		{
			headerName: 'Divergências',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';

				if (params.data.divergencia === true) {
					button.className = 'feather icon-alert-triangle text-warning';
					button.addEventListener('click', (e) => {
						e.stopPropagation();
						this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
					});
				} else {
					button.className = 'feather icon-check text-success';
				}
				return button;
			},
			minWidth: 100,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Condições',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';

				if (params.data.condicao === true) {
					button.className = 'feather icon-alert-triangle text-danger';
					button.addEventListener('click', (e) => {
						e.stopPropagation();
						this.abrirDialogoNotificacao(TipoDialogoDescList.condicoes, params.data.condicoes);
					});
				} else {
					button.className = 'feather icon-check text-success';
				}

				return button;
			},
			minWidth: 100,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Detalhes',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.className = 'feather icon-file-text';
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';
				button.addEventListener('click', (e) => {
					e.stopPropagation();
					this.navegaParaDetalhes({ data: params.data });
				});

				return button;
			},
			maxWidth: 120,
			sortable: false,
			filter: false,
		},
	];

	dadosTabela = signal<Ressarcimento[]>([]);
	carregando = signal(false);
	dadosRessarcimento = signal<Ressarcimento[]>([]);
	globalRessarcimentos = inject(DialogoService)
	private dialogoService = inject(DialogoService);
	private router = inject(Router);

	filtraLinha(agregador: any): boolean {
		const valores = [
			(agregador.numeroAgregador ?? '').toString().toLowerCase(),
			(agregador.fornecedor?.razaoSocial ?? '').toLowerCase(),
			agregador.dataHoraAutorizacao ? new Date(agregador.dataHoraAutorizacao).toLocaleDateString('pt-BR') : '',
			agregador.dataHoraAutorizacaoFinal ? new Date(agregador.dataHoraAutorizacaoFinal).toLocaleDateString('pt-BR') : '',
			(agregador.documentoNumero ?? '').toString().toLowerCase(),
			(agregador.valorPago ?? '').toString().toLowerCase(),
			(agregador.valorPagar ?? '').toString().toLowerCase(),
			(agregador.valorRessarcido ?? '').toString().toLowerCase(),
			(agregador.valorRessarcir ?? '').toString().toLowerCase(),
			(agregador.ativoCodigo ?? '').toString().toLowerCase(),
			(agregador.ativoStatus.descricao ?? '').toLowerCase(),
		];
		return this.filtrosApi.every((filtro, idx) => {
			return !filtro.valor || valores[idx].includes(filtro.valor.toString().toLowerCase());
		});
	}

	navegaParaDetalhes(event: any) {
		this.router.navigate(['/movimentacoes/detalhes'], {
			queryParams: { idMovimentacao: event.data.idMovimentacao },
		});
	}

	public abrirDialogoNotificacao(tipo: TipoDialogoDescList, itens: any[]): void {
			const titulo = DialogoDescricaoListaUtil.obterTituloPadrao(tipo);
			const configCss: Partial<ConfiguracaoDialogoLista> = DialogoDescricaoListaUtil.obterConfiguracaoPadrao(tipo) || {};
			const descricao = itens;
	
			this.dialogoService
				.dialogoNotificacaoLista({
					titulo,
					descricao,
					configCss,
					botaoConfirmar: { texto: 'Fechar', classe: 'btn btn-primary' },
				})
				.subscribe();
		}

	onGridReady() {
		// Retorno da tabela
	}

	constructor(private cdr: ChangeDetectorRef) {	}

	pesquisar() {
             this.ressarcimentosService.listarRessarcimentosCobrados(this.numeroSinistro, this.fornecedor, this.dataAutorizacaoInicial, this.dataAutorizacaoFinal, this.numeroDocumento, this.statusReparo, this.placa, this.divergencias, this.condicoes).subscribe({
			next: (dados) => {
				console.log(dados);
				this.dadosTabela.set(dados);
				this.cdr.detectChanges();
			},
			error: (err) => {
				this.erro.set('Erro ao carregar dados');
			}
		}); 
    }
}
