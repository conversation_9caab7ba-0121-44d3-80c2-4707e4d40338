import { Component, inject, signal, ChangeDetectorRef } from '@angular/core';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ColDef } from 'ag-grid-community';
import { TabelaPadraoComponent } from '../../../shared/components/tabelas/tabela-padrao/tabela-padrao.component';
import { PessoaService } from '../../../core/services/api/pessoa.service';
import { PessoaListagem } from '../../../shared/interfaces/api/pessoa-listagem.interface';
import { TipoPessoa } from '../../movimentacoes/movimentacoes.enum';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
	selector: 'app-pessoa-juridica',
	imports: [CommonModule, FormsModule, NgMultiSelectDropDownModule, RouterModule, CardComponent, TabelaPadraoComponent, NgxMaskDirective],
	templateUrl: './pessoa-juridica-listagem.component.html',
	styleUrl: './pessoa-juridica-listagem.component.scss',
})
export class PessoaJuridicaListagemComponent  {
	public columnDefs: ColDef[] = [
		{ field: 'razaoSocial', headerName: 'Razão Social', flex: 0.35 },
		{ field: 'nomeFantasia', headerName: 'Nome Fantasia', flex: 0.35 },
		{ 
			field: 'cnpj', 
			headerName: 'CNPJ', 
			flex: 0.2,
			valueFormatter: (params: any) => {
				const cnpj = params.value?.replace(/\D/g, '');
				if (!cnpj || cnpj.length !== 14) return params.value;
				return cnpj.replace(
					/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
					'$1.$2.$3/$4-$5'
				);
			}
		},
		{
			headerName: 'Ações',
			cellRenderer: (params: any) => {
				const editButton = document.createElement('span');
				editButton.title = 'editar';
				editButton.style.cursor = 'pointer';
				editButton.style.fontSize = '20px';
				editButton.style.color = '#818181ff';
				editButton.className = 'feather icon-edit text-primary me-2';
				editButton.addEventListener('click', (e) => {
					e.stopPropagation();
					this.irParaEdicao(params.data.id);
				});
				const container = document.createElement('div');
				container.appendChild(editButton);
				return container;
			},
			flex: 0.1,
			sortable: false,
			filter: false,
		},
	];

	dadosPessoas = signal<PessoaListagem[]>([]);

	razaoSocial: string = '';
	nomeFantasia: string = '';
	cnpj: string = '';

	irParaInclusao() {
		this.router.navigate(['/cadastros/pessoajuridica/cadastro']);
	}

	irParaEdicao(id: number) {
		this.router.navigate(['/cadastros/pessoajuridica/cadastro'], { queryParams: { id } });
	}

	private pessoaService = inject(PessoaService);

	constructor(private router: Router,private cdr: ChangeDetectorRef) {	}

	Pesquisar(): void {
		this.pessoaService.listar(TipoPessoa.Juridica, this.razaoSocial, this.nomeFantasia, this.cnpj).subscribe({
			next: (dados) => {
				this.dadosPessoas.set(dados);
				this.cdr.detectChanges();
			}
		});
	}
}
