<app-card cardTitle="Pessoa Jurídica - Listagem" blockClass="p-0">
	<div class="row ps-4">

		<!-- RAZÃO SOCIAL -->
		<div class="col-4">
			<label>Razão Social</label>
			<ng-container>
				<input id="razaoSocial" type="text" class="form-control form-control-sm mb-4"
					[(ngModel)]="razaoSocial" />
			</ng-container>
		</div>

		<!-- NOME FANTASIA -->
		<div class="col-4">
			<label>Nome Fantasia</label>
			<ng-container>
				<input id="nomeFantasia" type="text" class="form-control form-control-sm mb-4"
					[(ngModel)]="nomeFantasia" />
			</ng-container>
		</div>

	</div>
	<div class="row ps-4 pe-4">

		<!-- CNPJ -->
		<div class="col-4">
			<label>CNPJ</label>
			<ng-container>
				<input id="cnpj" type="text" class="form-control form-control-sm mb-4" [(ngModel)]="cnpj"
					mask="00.000.000/0000-00" />
			</ng-container>
		</div>

		<div class="col-4"></div>
		<div class="col-4 d-flex justify-content-end align-items-end mb-3">

			<!-- BOTÃO PESQUISAR -->
			<div class="me-2">
				<button id="btnPesquisar" class="form-control form-control-sm btn btn-primary"
					(click)="Pesquisar()">Pesquisar</button>
			</div>

			<!-- BOTÃO NOVA PESSOA JURÍDICA -->
			<div>
				<button id="btnNovaPessoaJuridica" class="form-control form-control-sm btn btn-outline-primary"
					(click)="irParaInclusao()">Incluir</button>
			</div>
		</div>
	</div>

	<!-- LISTAGEM DE PESSOAS -->
	<div class="row ps-4 pe-4">
		<app-tabela-padrao id="tabelaPessoas" [columnDefs]="columnDefs" [pagination]="true"
			[rowData]="dadosPessoas()" />
	</div>

</app-card>