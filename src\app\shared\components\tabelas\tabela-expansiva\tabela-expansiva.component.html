<div class="table-responsive">
	<table class="table table-bordered table-hover table-sm align-middle tabela-fixa tabela-destaque">
		<thead>
			<tr class="header-tabela">
				@for (coluna of colunasTabelaPrincipal; track coluna.field) {
				<th  [ngStyle]="coluna.cellStyle" [class]="coluna.cellClass">{{ coluna.headerName }}</th>
				}
			</tr>
		</thead>
		<tbody>
			@for (item of dadosTabelaPrincipal; track $index) {
			<tr (click)="toggleExpandir(item)" class="linha-principal" [class.linha-expandida]="linhaExpandida === item" style="cursor: pointer">
				@for (coluna of colunasTabelaPrincipal; track coluna.field) {
				<td [ngStyle]="coluna.cellStyle" [class]="coluna.cellClass">
					@if (coluna.cellRenderer) {
					    <div [appCellRenderer]="coluna.cellRenderer" [cellRendererParams]="{ data: item, component: this }" class="text-center"></div>
					} @else {
                        <span [innerHTML]="formataValor(obterValor(item, coluna), coluna)"></span>
					}
				</td>
				}
			</tr>
			@if (linhaExpandida === item) {
			<tr class="">
				<td [attr.colspan]="colunasTabelaPrincipal.length">
					<div [@expandirLinha]="'expandido'">
						<table class="table table-bordered table-sm mb-0 tabela-item">
							<thead >
								<tr class="header-tabela-colapsada">
									@for (coluna of colunasTabelaExpansao; track coluna.field) {
									<th [class]="coluna.cellClass">{{ coluna.headerName }}</th>
									}
								</tr>
							</thead>
							<tbody>
								@for (versao of item[propriedadeDadosExpansao]; track $index) {
								<tr>
									@for (coluna of colunasTabelaExpansao; track coluna.field) {
									<td  class="text-center" [class]="coluna.cellClass" [ngStyle]="coluna.cellStyle">
                                        @if (coluna.valueFormatter) {
                                            <span  [innerHTML]="formataValor(obterValor(versao, coluna), coluna)"></span>
					                    } @else if (coluna.cellRenderer) {
                                            <div [appCellRenderer]="coluna.cellRenderer" [cellRendererParams]="{ data: versao, component: this }" class="text-center"></div>
                                        } @else {
                                            <span [innerHTML]="formataComQuebraDeLinha(obterValor(versao, coluna))"></span>
                                        }
									</td>
									}
								</tr>
								}
							</tbody>
						</table>
					</div>
				</td>
			</tr>
			}
			} @empty {
			<tr>
				<td [attr.colspan]="colunasTabelaPrincipal?.length" class="text-center">Nenhum dado encontrado.</td>
			</tr>
			}
		</tbody>
	</table>
</div>
