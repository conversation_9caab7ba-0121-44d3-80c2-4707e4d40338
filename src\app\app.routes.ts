import { Routes } from '@angular/router';
import { AuthGuard, LoginGuard } from './core/auth/guards/auth.guard';

export const routes: Routes = [
	{ path: '', redirectTo: '/login', pathMatch: 'full' },
	{
		path: 'login',
		loadComponent: () => import('./features/auth/login/login.component').then((m) => m.LoginComponent),
		canActivate: [LoginGuard],
	},
	{
		path: 'recuperar-senha',
		loadComponent: () => import('./features/auth/recuperar-senha/recuperar-senha.component').then((m) => m.RecuperarSenhaComponent),
		canActivate: [LoginGuard],
	},
	{
		path: 'cadastrar',
		loadComponent: () => import('./features/auth/cadastro-usuario/cadastro-usuario.component').then((m) => m.CadastroUsuarioComponent),
		canActivate: [LoginGuard],
	},
	{
		path: 'confirmacao',
		loadComponent: () => import('./features/auth/two-factor/two-factor.component').then((m) => m.TwoFactorComponent),
		// canActivate: [AuthGuard],
	},
	{
		path: 'confirmar-usuario',
		loadComponent: () =>
			import('./features/auth/cadastro-novo-usuario/cadastro-novo-usuario.component').then((m) => m.CadastroNovoUsuarioComponent),
	},
	{
		path: 'reset-password-basic',
		loadComponent: () => import('./features/auth/nova-senha/nova-senha.component').then((m) => m.NovaSenhaComponent),
	},
	{
		path: 'validacao-codigo/atualizacao-status-veiculo',
		loadComponent: () => import('./features/auth/validacao-codigo/validacao-codigo.component').then((m) => m.ValidacaoCodigoComponent),
	},
	{
		path: 'validacao-codigo/pagamento-ressarcimento',
		loadComponent: () => import('./features/auth/validacao-codigo/validacao-codigo.component').then((m) => m.ValidacaoCodigoComponent),
	},
	{
		path: 'atualizacao-status-veiculo',
		loadComponent: () =>
			import('./features/oficina/atualizacao-status-veiculo/atualizacao-status-veiculo.component').then(
				(m) => m.AtualizacaoStatusVeiculoComponent
			),
	},
	{
		path: 'pagamento-ressarcimento',
		loadComponent: () =>
			import('./features/fornecedores/pagamento-ressarcimento/pagamento-ressarcimento.component').then((m) => m.PagamentoRessarcimentoComponent),
	},
	{
		path: '',
		loadComponent: () => import('./features/spa/spa.component').then((m) => m.SpaComponent),
		// canActivate: [AuthGuard],
		children: [
			{
				path: 'pagina-inicial',
				loadComponent: () =>
					import('./features/pagina-inicial/pagina-inicial.component').then((m) => m.PaginaInicialComponent),
			},
			{
				path: 'movimentacoes',
				loadComponent: () => import('./features/movimentacoes/movimentacoes.component').then((m) => m.MovimentacoesComponent),
			},
			{
				path: 'movimentacoes/detalhes',
				loadComponent: () =>
					import('./features/movimentacoes-detalhes/movimentacoes-detalhes.component').then((m) => m.MovimentacoesDetalhesComponent),
			},
			{
				path: 'pagamentos',
				loadComponent: () => import('./features/pagamentos/pagamentos.component').then((m) => m.PagamentosComponent),
			},
			{
				path: 'ressarcimentos',
				loadComponent: () => import('./features/ressarcimentos/ressarcimentos.component').then((m) => m.RessarcimentosComponent),
			},
			{
				path: 'configuracoes',
				loadComponent: () => import('./features/configuracoes/configuracoes.component').then((m) => m.ConfiguracoesComponent),
			},
			{
				path: 'novo-usuario',
				loadComponent: () => import('./features/enviar-convite/enviar-convite.component').then((m) => m.EnviarConviteComponent),
			},
			{
				path: 'relatorios',
				loadComponent: () => import('./features/em-construcao/em-construcao.component').then((m) => m.EmConstrucaoComponent),
				children: [
					{
						path: 'status',
						loadComponent: () => import('./features/em-construcao/em-construcao.component').then((m) => m.EmConstrucaoComponent),
					},
				],
			},
			{
				path: 'relatorios/periodo',
				loadComponent: () =>
					import('./features/relatorios/consolidado-periodo/consolidado-periodo.component').then((m) => m.ConsolidadoPeriodoComponent),
			},
			{
				path: 'cadastros/pessoajuridica/listagem',
				loadComponent: () => import('./features/cadastros/pessoa-juridica/pessoa-juridica-listagem.component').then((m) => m.PessoaJuridicaListagemComponent),
			},
			{
				path: 'cadastros/pessoajuridica/cadastro',
				loadComponent: () => import('./features/cadastros/pessoa-juridica/pessoa-juridica-cadastro.component').then((m) => m.PessoaJuridicaCadastroComponent),
			},
			{
				path: 'cadastros',
				loadComponent: () => import('./features/em-construcao/em-construcao.component').then((m) => m.EmConstrucaoComponent),
			},
			{
				path: 'regras-processamento',
				loadComponent: () =>
					import('./features/regras-processamento/regras-processamento.component').then((m) => m.RegrasProcessamentoComponent),
			},
		],
	},

	{ path: '**', redirectTo: '/login' },
];
