import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { VeiculosResponse, VeiculoResponse, Veiculo } from '../../../shared/interfaces/api/veiculo.interface';

@Injectable({
  providedIn: 'root'
})
export class VeiculoService {
  private readonly API_BASE = environment.apiUrlAuth;

  constructor(private http: HttpClient) {}

  todos(): Observable<VeiculosResponse> {
    return this.http.get<VeiculosResponse>(`${this.API_BASE}/veiculos`);
  }

  getById(id: number): Observable<VeiculoResponse> {
    return this.http.get<VeiculoResponse>(`${this.API_BASE}/veiculos/${id}`);
  }

  criar(veiculo: Veiculo): Observable<VeiculoResponse> {
    return this.http.post<VeiculoResponse>(`${this.API_BASE}/veiculos`, veiculo);
  }

  atualizar(id: number, veiculo: Veiculo): Observable<VeiculoResponse> {
    return this.http.put<VeiculoResponse>(`${this.API_BASE}/veiculos/${id}`, veiculo);
  }

  deletar(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/veiculos/${id}`);
  }
}
