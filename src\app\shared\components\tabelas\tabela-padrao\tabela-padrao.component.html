<div class="ag-grid-container" [ngStyle]="rowData.length > 0 ? { 'height': height } : { 'height': '180px' }">
    <ag-grid-angular
    style="width: 100%; height: 100%; border-radius: 0px;"
    [columnDefs]="processedColumnDefs"
    [rowData]="rowData"
    [defaultColDef]="defaultColDef"
    [pagination]="pagination"
    [paginationAutoPageSize]="paginationAutoPageSize"
    [paginationPageSize]="paginationPageSize"
    [enableRtl]="enableRtl"
    [theme]="tema"
    [localeText]="localeText"
    [tooltipShowDelay]="tooltipShowDelay"
    [tooltipHideDelay]="tooltipHideDelay"
    [rowSelection]="rowSelection"
    [suppressRowClickSelection]="suppressRowClickSelection"
    [enableCellTextSelection]="enableCellTextSelection"
    [rowHeight]="rowHeight"
    [headerHeight]="headerHeight"
    [domLayout]="domLayout"
    [rowModelType]="rowModelType"
    (gridReady)="onGridReady($event)"
    (rowClicked)="onRowClicked($event)"
    (cellClicked)="onCellClicked($event)"
    (rowSelected)="onRowSelected($event)"
    (sortChanged)="onSortChanged($event)"
  ></ag-grid-angular>
</div>
