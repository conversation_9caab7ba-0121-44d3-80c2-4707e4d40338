<div class="multi-select-container">
  <div class="multi-select-input" (click)="alternarDropdown()">
    <div class="multi-select-chips">
      <span *ngFor="let valor of selecionados" class="chip">
        {{ getNomeOpcao(valor) }}
        <span class="chip-close" (click)="$event.stopPropagation(); alternarSelecao(valor)">&times;</span>
      </span>
      <input
        type="text"
        [placeholder]="placeholder"
        [(ngModel)]="termoBusca"
        (click)="$event.stopPropagation()"
        (input)="$event.stopPropagation()"
      />
    </div>
    <span class="multi-select-arrow">&#9662;</span>
    <span *ngIf="selecionados.length > 0" class="multi-select-clear" (click)="limparSelecao(); $event.stopPropagation();">&times;</span>
  </div>
  <div class="multi-select-dropdown" *ngIf="aberto">
    <div class="multi-select-opcao">
      <input type="checkbox" [checked]="todosSelecionados()" (change)="selecionarTodos()" />
      <span class="fw-bold">Selecionar todos</span>
    </div>
    <div class="multi-select-opcao" *ngFor="let opcao of opcoesFiltradas">
      <input type="checkbox" [checked]="estaSelecionado(opcao.valor)" (change)="alternarSelecao(opcao.valor)" />
      <span>{{ opcao.nome }}</span>
    </div>
  </div>
</div>
