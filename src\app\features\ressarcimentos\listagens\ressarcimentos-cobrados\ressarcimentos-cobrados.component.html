<div class="row ps-4" style="padding-right: 30px;">
		<div class="col-3">
			<label>Nº Sinistro</label>
			<ng-container>
				<input id="numeroSinistro" type="text" class="form-control form-control-sm mb-4" [(ngModel)]="numeroSinistro" />
			</ng-container>
		</div>
        <div class="col-5">
			<label>Fornecedor</label>
			<ng-container>
				<input id="fornecedor" type="text" class="form-control form-control-sm mb-4" [(ngModel)]="fornecedor" />
			</ng-container>
		</div>
        <div class="col-2">
            <label>Data Autorização Inicial</label>
            <ng-container>
                <input id="dataAutorizacaoInicial" type="date" class="form-control form-control-sm mb-4" [(ngModel)]="dataAutorizacaoInicial" />
            </ng-container>
        </div>
        <div class="col-2">
            <label>Data Autorização Final</label>
            <ng-container>
                <input id="dataAutorizacaoFinal" type="date" class="form-control form-control-sm mb-4" [(ngModel)]="dataAutorizacaoFinal" />
            </ng-container>
        </div>
</div>
<div class="row ps-4" style="padding-right: 30px;">
    <div class="col-3">
		<label>NF</label>
		<ng-container>
			<input id="numeroDocumento" type="text" class="form-control form-control-sm mb-4" [(ngModel)]="numeroDocumento" />
		</ng-container>
	</div>
    <div class="col-3">
        <label>Status Reparo</label>
        <ng-container>
            <select id="statusReparo" class="form-control form-control-sm mb-4" [(ngModel)]="statusReparo">
                <option value="">Selecione</option>
                <option *ngFor="let status of statusReparoEnum" [value]="status.valor">{{ status.valor }}</option>
            </select>
        </ng-container>
    </div>
    <div class="col-3">
		<label>Placa</label>
		<ng-container>
			<input id="placa" type="text" class="form-control form-control-sm mb-4" [(ngModel)]="placa" />
		</ng-container>
	</div>
    <div class="col-3">
        <label>Divergência</label>
        <ng-container>
            <select id="divergencias" class="form-control form-control-sm mb-4" [(ngModel)]="divergencias">
                <option value="">Selecione</option>
                <option value="true">Sim</option>
                <option value="false">Não</option>
            </select>
        </ng-container>
    </div>
</div>
<div class="row ps-4" style="padding-right: 30px;">
    <div class="col-3">
        <label>Condição</label>
        <ng-container>
            <select id="condicoes" class="form-control form-control-sm mb-4" [(ngModel)]="condicoes">
                <option value="">Selecione</option>
                <option value="true">Sim</option>
                <option value="false">Não</option>
            </select>
        </ng-container>
    </div>
    <div class="col-2 d-flex align-items-end">
        <button type="button" class="btn btn-primary w-100 mb-4" (click)="pesquisar()">
            Pesquisar
        </button>
    </div>
</div>

<div class="row ps-4 pe-4">
		<app-tabela-padrao id="dadosTabela" [columnDefs]="columnDefs" [pagination]="true" [rowData]="dadosTabela()" />
</div>
