export interface Fornecedor {
	id: number;
	razaoSocial: string;
	nomeFantasia: string;
	cnpj: string;
}

export interface Situacao {
	valor: number;
	descricao: string;
}

export interface AtivoStatus {
	valor: number;
	descricao: string;
}

export interface Movimentacao {
	id: number;
	idMovimentacao: number;
	idAgregador: number | null;
	numeroAgregador: string | null;
	fornecedor: Fornecedor;
	dataAutorizacao: string | null;
	documentoId: number | null;
	documentoNumero: string | null;
	valorPagar: number;
	valorPago: number;
	valorRessarcir: number;
	valorRessarcido: number;
	pagoSemExclusao: number;
	exclusaoAntesPagamento: number;
	situacao: Situacao;
	ativoCodigo: string;
	ativoStatus: AtivoStatus;
	divergencias: any[];
	condicoes: any[];
}
