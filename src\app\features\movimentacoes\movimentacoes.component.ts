import { Component, inject, OnInit, signal, ChangeDetectorRef } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';
import { DialogoService } from '../../core/services/dialogos/dialogo.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MovimentacoesService } from '../../core/services/api/movimentacoes.service';
import { GlobalService } from '../../core/services/global.service';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Movimentacao } from '../../shared/interfaces/api/movimentacao.interface';
import { CardComponent } from '../../shared/components/card/card.component';
import {
	ColDef,
	ModuleRegistry,
	AllCommunityModule,
} from 'ag-grid-community';
import { TabelaPadraoComponent } from "../../shared/components/tabelas/tabela-padrao/tabela-padrao.component";
import { ConfiguracaoDialogoLista, DialogoDescricaoListaUtil } from '../../shared/utils/dialogo-descricao-lista.util';
import { TipoDialogoDescList } from '../../shared/enums/dialogos/tipo-dialogo-descricao-list.enum';
import { TipoDescricaoDialogo } from '../../shared/enums/dialogos/tipo-descricao-dialogo.enum';

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);

@Component({
	selector: 'app-movimentacoes',
	standalone: true,
	imports: [CommonModule, FormsModule, NgMultiSelectDropDownModule, RouterModule, CardComponent, TabelaPadraoComponent],
	templateUrl: './movimentacoes.component.html',
	styleUrl: './movimentacoes.component.scss',
})
export class MovimentacoesComponent implements OnInit {

	// Getters para totais das colunas
	get totalValorPagar(): number {
		return this.dadosMovimentacoes().filter((a: any) => this.filtraLinha(a)).reduce((acc: number, a: any) => acc + (a.valorPagar || 0), 0);
	}

	get totalValorPago(): number {
		return this.dadosMovimentacoes().filter((a: any) => this.filtraLinha(a)).reduce((acc: number, a: any) => acc + (a.valorPago || 0), 0);
	}

	get totalValorRessarcir(): number {
		return this.dadosMovimentacoes().filter((a: any) => this.filtraLinha(a)).reduce((acc: number, a: any) => acc + (a.valorRessarcir || 0), 0);
	}

	get totalValorRessarcido(): number {
		return this.dadosMovimentacoes().filter((a: any) => this.filtraLinha(a)).reduce((acc: number, a: any) => acc + (a.valorRessarcido || 0), 0);
	}

	public filtrosApi: { label: string; valor: string | string[] }[] = [
		{ label: 'Nº Sinistro', valor: '' },
		{ label: 'Fornecedor', valor: '' },
		{ label: 'Data Autorização', valor: '' },
		{ label: 'NF', valor: '' },
		{ label: 'Situação', valor: '' },
		{ label: 'Pago', valor: '' },
		{ label: 'A pagar', valor: '' },
		{ label: 'Ressarcir', valor: '' },
		{ label: 'Ressarcido', valor: '' },
		{ label: 'Placa', valor: '' },
		{ label: 'Status Reparo', valor: '' },
	];

	public columnDefs: ColDef[] = [
		{ field: 'numeroAgregador', headerName: 'Nº Sinistro', minWidth: 120 },
		{ field: 'fornecedor.nomeFantasia', headerName: 'Fornecedor', minWidth: 120 },
		{ field: 'dataAutorizacao', headerName: 'Data Autorização', minWidth: 120},
		{ field: 'documentoNumero', headerName: 'NF' },
		{ field: 'situacao.descricao', headerName: 'Situação', minWidth: 120, tooltipField: 'situacao.descricao' },
		{
			field: 'valorPagar',
			headerName: 'A pagar',
			minWidth: 120,
			valueFormatter: (params) =>
				typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			cellStyle: { textAlign: 'center' },
		},
		{
			field: 'valorPago',
			headerName: 'Pago',
			minWidth: 120,
			valueFormatter: (params) =>
				typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			cellStyle: { textAlign: 'center' },
		},
		{
			field: 'valorRessarcir',
			headerName: 'Ressarcir',
			minWidth: 120,
			valueFormatter: (params) =>
				typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			cellStyle: { textAlign: 'center' },
		},
		{
			field: 'valorRessarcido',
			headerName: 'Ressarcido',
			minWidth: 120,
			valueFormatter: (params) =>
				typeof params.value === 'number' ? params.value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : '',
			cellStyle: { textAlign: 'center' },
		},
		{ field: 'ativoCodigo', headerName: 'Placa' },
		{ field: 'ativoStatus.descricao', headerName: 'Status Reparo', minWidth: 150, tooltipField: 'ativoStatus.descricao'  },
		{
			headerName: 'Divergências',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';

				if (params.data.divergencias && params.data.divergencias.length > 0) {
					button.className = 'feather icon-alert-triangle text-danger';
					button.addEventListener('click', (e) => {
						e.stopPropagation();
						this.abrirDialogoNotificacao(TipoDialogoDescList.divergencias, params.data.divergencias);
					});
				} else {
					button.className = 'feather icon-check text-success';
				}

				return button;
			},
			minWidth: 100,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Condições',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';
				
				if (params.data.condicoes && params.data.condicoes.length > 0) {
					button.className = 'feather icon-alert-triangle text-warning';
					button.addEventListener('click', (e) => {
						e.stopPropagation();
						this.abrirDialogoNotificacao(TipoDialogoDescList.condicoes, params.data.condicoes);
					});
				} else {
					button.className = 'feather icon-check text-success';
				}

				return button;
			},
			minWidth: 100,
			sortable: false,
			filter: false,
		},
		{
			headerName: 'Detalhes',
			cellRenderer: (params: any) => {
				const button = document.createElement('span');
				button.className = 'feather icon-file-text';
				button.style.cursor = 'pointer';
				button.style.fontSize = '16px';
				button.style.color = '#818181ff';
				button.addEventListener('click', (e) => {
					e.stopPropagation();
					this.navegaParaDetalhes({ data: params.data });
				});

				return button;
			},
			maxWidth: 120,
			sortable: false,
			filter: false,
		},
	];

	dadosTabela: Movimentacao[] = [];
	carregando = signal(false);
	erro = signal('');
	dadosMovimentacoes = signal<Movimentacao[]>([]);
	globalTenantId: ReturnType<GlobalService['getDadoCompartilhado']>;
	showGrid: boolean = true;

	private router = inject(Router);
	private cdr = inject(ChangeDetectorRef);
	private movimentacoesService = inject(MovimentacoesService);
	private globalService = inject(GlobalService);
	private dialogoService = inject(DialogoService);
	private sanitizer = inject(DomSanitizer)
	private route = inject(ActivatedRoute);


	constructor(
	) {
		this.globalTenantId = this.globalService.getDadoCompartilhado();
	}

	ngOnInit(): void {
		this.carregarMovimentacoes();
	}

	carregarMovimentacoes(): void {
		this.carregando.set(true);
		this.erro.set('');

		this.movimentacoesService.todos(this.globalTenantId()).subscribe({
			next: (dados) => {
				// Aplicar filtros dos query params
				this.aplicarFiltrosQueryParams(dados);

				this.dadosMovimentacoes.set(this.dadosTabela);
				this.carregando.set(false);
				this.cdr.detectChanges();
			},
			error: (err) => {
				console.error('Erro ao carregar dados do service:', err);
				this.erro.set('Erro ao carregar os dados. Por favor, tente novamente.');
				this.carregando.set(false);
			},
		});
	}

	private aplicarFiltrosQueryParams(dados: Movimentacao[]): void {
		this.route.queryParams.subscribe((params) => {
			const filtro = params['filtro'];

			if (filtro === 'pagoSemExclusao') {
				this.dadosTabela = dados.filter((item: Movimentacao) => item.pagoSemExclusao > 0);
			} else if (filtro === 'exclusaoAntesPagamento') {
				this.dadosTabela = dados.filter((item: Movimentacao) => item.exclusaoAntesPagamento > 0);
			} else {
				this.dadosTabela = dados;
			}
		});
	}

	filtraLinha(agregador: any): boolean {
		const valores = [
			(agregador.numeroAgregador ?? '').toString().toLowerCase(),
			(agregador.fornecedor?.razaoSocial ?? '').toLowerCase(),
			agregador.dataHoraAutorizacao ? new Date(agregador.dataHoraAutorizacao).toLocaleDateString('pt-BR') : '',
			(agregador.documentoNumero ?? '').toString().toLowerCase(),
			(agregador.situacao.descricao ?? '').toString().toLowerCase(),
			(agregador.valorPago ?? '').toString().toLowerCase(),
			(agregador.valorPagar ?? '').toString().toLowerCase(),
			(agregador.valorRessarcido ?? '').toString().toLowerCase(),
			(agregador.valorRessarcir ?? '').toString().toLowerCase(),
			(agregador.ativoCodigo ?? '').toString().toLowerCase(),
			(agregador.ativoStatus.descricao ?? '').toLowerCase(),
		];
		return this.filtrosApi.every((filtro, idx) => {
			return !filtro.valor || valores[idx].includes(filtro.valor.toString().toLowerCase());
		});
	}

	public abrirDialogoNotificacao(tipo: TipoDialogoDescList, itens: any[]): void {
		const titulo = DialogoDescricaoListaUtil.obterTituloPadrao(tipo);
		const configCss: Partial<ConfiguracaoDialogoLista> = DialogoDescricaoListaUtil.obterConfiguracaoPadrao(tipo) || {};
		const descricao = itens;

		this.dialogoService
			.dialogoNotificacaoLista({
				titulo,
				descricao,
				configCss,
				botaoConfirmar: { texto: 'Fechar', classe: 'btn btn-primary' },
			})
			.subscribe();
	}

	navegaParaDetalhes(event: any) {
		this.router.navigate(['/movimentacoes/detalhes'], {
			queryParams: { idMovimentacao: event.data.idMovimentacao },
		});
	}

	onGridReady() {
		// Retorno da tabela
	}
}

