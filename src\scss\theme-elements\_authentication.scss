/**  =====================
      Authentication css start
==========================  **/
.auth-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	min-width: 100%;
	min-height: 100vh;

	a,
	p > a {
		color: $theme-heading-color;
		font-weight: 600;
	}

	.btn-auth-gen {
		.btn-icon {
			width: 140px;
			height: 140px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 45px;

			small {
				font-size: 15px;
			}
		}
	}

	.card {
		margin-bottom: 0;
	}

	> div {
		z-index: 5;
	}

	.auth-content {
		position: relative;
		width: 390px;
		padding: 15px;

		&.multyform,
		&.subscribe {
			width: 750px;
		}
	}
	@media only screen and (max-width: 768px) {
		max-width: 360px;
	}
	@media only screen and (max-width: 575px) {
		.card {
			.card-body {
				padding: 30px 15px;
			}
		}
	}

	.auth-icon {
		font-size: 30px;

		&:before {
			background: $primary-color;
			background-clip: text;
			text-fill-color: transparent;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	&.offline {
		background-image: none;

		&:before {
			display: none;
		}
	}

	.circulo-logo {
		width: 80px;
		height: 80px;
    border-radius: 50%;


		&:nth-child(2) {
			top: 150px;
			right: -150px;
			background: $primary-color;
		}

		&:nth-child(3) {
			left: -150px;
			bottom: 150px;
			background: #1d56e9;
		}
	}

// 	.auth-bg {
// 		.r {
// 			position: absolute;
// 			width: 300px;
// 			height: 300px;
// 			// border-left: 150px solid transparent;
// 			// border-right: 150px solid transparent;
// 			// border-bottom: 300px solid #3498db; /* Escolha a cor do triângulo */
// 			border-radius: 50%;

// 			&:first-child {
// 				top: -100px;
// 				right: -100px;
// 				background: linear-gradient(-135deg, #283eeb 0%, #4b93a3 100%);
// 			}

// 			&:last-child {
// 				left: -100px;
// 				bottom: -100px;
// 				background: $theme-color2;
// 			}

// 			&.s {
// 				width: 20px;
// 				height: 20px;
// 				&:nth-child(2) {
// 					top: 150px;
// 					right: -150px;
// 					background: $primary-color;
// 				}

// 				&:nth-child(3) {
// 					left: -150px;
// 					bottom: 150px;
// 					background: $success-color;
// 				}
// 			}

// 			&:nth-child(odd) {
// 				animation: floating 7s infinite;
// 			}

// 			&:nth-child(even) {
// 				animation: floating 9s infinite;
// 			}
// 		}
// 	}
// }
// @keyframes floating {
// 	from {
// 		transform: rotate(0deg) translate(-10px) rotate(0deg);
// 	}

// 	to {
// 		transform: rotate(360deg) translate(-10px) rotate(-360deg);
// 	}

.auth-bg {
  position: relative;
  
  .r {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    opacity: 0;
    transform: scale(0.9);

    &.from-top-right {
      top: -100px;
      right: -100px;
      background: linear-gradient(-135deg, #283eeb 0%, #4b93a3 100%);
      animation: moveToCenterTR 3s 0.2s cubic-bezier(0.2, 0.1, 0.1, .2) forwards;
    }

    &.from-bottom-left {
      left: -100px;
      bottom: -100px;
      background: $theme-color;
      animation: moveToCenterBL 3s 0.2s cubic-bezier(0.2, 0.1, 0.1, .2) forwards;
    }

    &.s {
      width: 20px;
      height: 20px;
      opacity: 0;
      transform: scale(0.7);

      &.from-top-left {
        top: -20px;
        left: -20px;
        background: $primary-color;
        animation: moveToCenterTL 4s 0.4s cubic-bezier(0.2, 0.8, 0.7, 1.2) forwards;
      }

      &.from-bottom-right {
        bottom: -20px;
        right: -20px;
        background: $success-color;
        animation: moveToCenterBR 4s 0.4s cubic-bezier(0.2, 0.8, 0.7, 1.2) forwards;
        
      }
    }
  }
}

@keyframes moveToCenterTR {
  0% {
    opacity: 0;
    transform: scale(0.7) translate(0, 0);
  }
  50% {
    opacity: 0.4;
    transform: scale(1) translate(-40vw, 80vh);
  }
  100% {
    opacity: 0.7;
    top: 100%;
    right: 70%;
    transform: translate(25%, 89%) scale(1);
  }
}

@keyframes moveToCenterBL {
  0% {
    opacity: 0;
    transform: scale(0.7) translate(0, 0);
  }
  50% {
    opacity: 0.2;
    transform: scale(1) translate(80vw, -30vh);
  }
  100% {
    opacity: 0.4;
    left: 30%;
    bottom: 1%;
    transform: translate(45%, 50%) scale(0.9);
  }
}

@keyframes moveToCenterTL {
  0% {
    opacity: 0;
    transform: scale(0.7) translate(1, 1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1) translate(50vw, 50vh);
  }
  100% {
    opacity: 0.7;;
    top: 50%;
    left: 50%;
    transform: translate(112%, -243%) scale(1.5);
  }
}

@keyframes moveToCenterBR {
  0% {
    opacity: 0;
    transform: scale(0.7) translate(0, 0);
  }
  50% {
    opacity: 0.4;
    transform: scale(1) translate(-50vw, -50vh);
  }
  100% {
    opacity: 0.7;;
    bottom: -57vh;
    right: 15vh;
    transform: translate(20%, 20%) scale(1);
  }
}
}
/**====== Authentication css end ======**/
