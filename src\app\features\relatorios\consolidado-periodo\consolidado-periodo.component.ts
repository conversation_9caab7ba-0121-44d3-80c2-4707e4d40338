import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { DecimalPipe, CommonModule } from '@angular/common';
import { PaginaInicialService } from '../../../core/services/api/pagina-inicial.service';
import { GlobalService } from '../../../core/services/global.service';
import { CardComponent } from '../../../shared/components/card/card.component';
import { Router } from '@angular/router';
import { ApexOptions, NgApexchartsModule } from 'ng-apexcharts';
import { PainelPrincipal } from '../../../shared/interfaces/api/painel-principal.interface';
import { HttpClient } from '@angular/common/http';
import { MockDataService } from '../../../shared/services/mock-data.service';
import { FormsModule } from '@angular/forms';

@Component({
	selector: 'app-painel-principal',
	imports: [NgApexchartsModule, CardComponent, CommonModule, FormsModule],
	templateUrl: './consolidado-periodo.component.html',
	styleUrls: ['./consolidado-periodo.component.scss'],
	providers: [DecimalPipe],
})
export class ConsolidadoPeriodoComponent implements OnInit {
	cardHovered = false;
	cardHoveredPagar = false;
	cardHoveredPagoSem = false;
	cardHoveredPagoAntes = false;
	cardHoveredPagarIncluidas = false;
	cardHoveredPagarPendentes = false;

	dataInicio: Date | null = null;
  	dataFim: Date | null = null;

	/**
	 * Retorna o valor do stroke-dasharray para um círculo SVG, dado a porcentagem.
	 * @param porcentagem Valor de 0 a 100
	 */
	getStrokeDasharray(porcentagem?: number): string {
		const r = 50;
		const circunferencia = 2 * Math.PI * r;
		const pct = Math.max(0, Math.min(100, porcentagem ?? 0));
		const dash = (pct / 100) * circunferencia;
		const gap = circunferencia - dash;
		return `${dash} ${gap}`;
	}

	/**
	 * Retorna o valor do stroke-dashoffset para posicionar o início do círculo.
	 * @param porcentagem Soma das porcentagens anteriores para "empilhar" os círculos
	 */
	getStrokeDashoffset(porcentagem?: number): string {
		const r = 50;
		const circunferencia = 2 * Math.PI * r;
		const pct = Math.max(0, Math.min(100, porcentagem ?? 0));
		const offset = (pct / 100) * circunferencia;
		return `-${offset}`;
	}
	painel: PainelPrincipal = {} as PainelPrincipal;
	globalTenantId: ReturnType<GlobalService['getDadoCompartilhado']>;
	areaAngleChart: Partial<ApexOptions>;

	constructor(
		private cdr: ChangeDetectorRef,
		private painelPrincipalService: PaginaInicialService,
		private globalService: GlobalService,
		private http: HttpClient,
		private router: Router,
		private mockDataService: MockDataService
	) {
		this.globalTenantId = this.globalService.getDadoCompartilhado();

		// Carrega os dados do painel ao iniciar o componente
		// this.carregaPainel();

		this.areaAngleChart = {
			chart: {
				height: 380,
				type: 'area',
				stacked: false,
			},
			stroke: {
				curve: 'straight',
			},
			series: [
				{
					name: 'Autorizados',
					data: [
						{
							x: 'Janeiro',
							y: 612.66,
						},
						{
							x: 'Fevereiro',
							y: 311.83,
						},
						{
							x: 'Março',
							y: 356.76,
						},
						{
							x: 'Abril',
							y: 1500.0,
						},
						{
							x: 'Maio',
							y: 1210.0,
						},
						{
							x: 'Junho',
							y: 1160.0,
						},
					],
				},
				{
					name: 'Pagos',
					data: [
						{
							x: 'Janeiro',
							y: 0.0,
						},
						{
							x: 'Fevereiro',
							y: 0.0,
						},
						{
							x: 'Março',
							y: 365.76,
						},
						{
							x: 'Abril',
							y: 1500.0,
						},
						{
							x: 'Maio',
							y: 1000.0,
						},
						{
							x: 'Junho',
							y: 0.0,
						},
					],
				},
				{
					name: 'Ressarcidos',
					data: [
						{
							x: 'Janeiro',
							y: 0.0,
						},
						{
							x: 'Fevereiro',
							y: 0.0,
						},
						{
							x: 'Março',
							y: 0.0,
						},
						{
							x: 'Abril',
							y: 0.0,
						},
						{
							x: 'Maio',
							y: 300.0,
						},
						{
							x: 'Junho',
							y: 0,
						},
					],
				},
				{
					name: 'Exclusões',
					data: [
						{
							x: 'Janeiro',
							y: 0.0,
						},
						{
							x: 'Fevereiro',
							y: 126.73,
						},
						{
							x: 'Março',
							y: 365.76,
						},
						{
							x: 'Abril',
							y: 0.0,
						},
						{
							x: 'Maio',
							y: 300,
						},
						{
							x: 'Junho',
							y: 0.0,
						},
					],
				},
			],
			tooltip: {
				followCursor: true,
			},
			fill: {
				opacity: 1,
			},
		};
	}

	ngOnInit(): void {
		// this.carregaPainel();
		// this.carregaMock();
		this.painel = this.mockDataService.mockPainelPrincipal();
	}

	carregaPainel(): void {
		this.painelPrincipalService.todos(this.globalTenantId()).subscribe({
			next: (data) => {
				if (data) {
					// this.painel = data;
					this.cdr.detectChanges();
				} else {
					this.carregaMock();
				}
			},
			error: (error) => {
				console.error('Erro ao carregar painel:', error);
				this.carregaMock();
			},
		});
	}

	private carregaMock(): void {
		this.http.get<any>('http://localhost:3000/painel-principal').subscribe({
			next: (data) => {
				this.painel = data[0];
				this.cdr.detectChanges();
			},
			error: (error) => {
				console.error('Erro ao carregar painel:', error);
			},
		});
	}

	direcionarParaRessarcimentos(filtro: string): void {
		this.router.navigate(['/ressarcimentos'], {
			queryParams: { filtro },
		});
	}

	direcionarParaPagamentos(filtro: string): void {
		this.router.navigate(['/pagamentos'], {
			queryParams: { filtro },
		});
	}

	direcionarParaMovimentacoes(filtro: string): void {
		this.router.navigate(['/movimentacoes'], {
			queryParams: { filtro },
		});
	}
}
