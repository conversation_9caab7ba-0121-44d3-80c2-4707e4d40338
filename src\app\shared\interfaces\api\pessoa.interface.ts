import { PessoaContato } from "./pessoa-contato.interface";
import { PessoaDadoBancario } from "./pessoa-dado-bancario.interface";
import { PessoaEndereco } from "./pessoa-endereco.interface";

export interface Pessoa {
  id?: number;
  TipoPessoa: number;
  razaoSocial?: string;
  nomeFantasia?: string;
  cnpj?: string;
  enderecos?: PessoaEndereco[];
  contatos?: PessoaContato[];
  dadosBancarios?: PessoaDadoBancario[];
}

export type PessoaResponse = Pessoa;
export type PessoasResponse = Pessoa[];
