<select
  class="form-select"
  [ngModel]="valorSelecionado"
  (ngModelChange)="mudouValor.emit($event)"
  aria-label="Seleção genérica"
>
  <option
    *ngIf="mostrarTextoPadrao"
    [value]="''"
    [selected]="!valorSelecionado"
    disabled
    hidden
  >{{ textoPadrao }}</option>
  <option
    *ngFor="let opcao of opcoes"
    [value]="opcao.valor"
    [selected]="opcao.valor == valorSelecionado"
  >
    {{ opcao.nome }}
  </option>
</select>