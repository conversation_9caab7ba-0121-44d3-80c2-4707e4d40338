import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../auth/services/auth.service';
import { MockDataService } from '../../../shared/services/mock-data.service';

@Injectable({
  providedIn: 'root'
})
export class DocumentosService {
  private readonly API_BASE = environment.useProxy ? '/api-cors/documentos' : `${environment.apiUrl}/documentos`;
  // private readonly API_BASE = `http://localhost:5010/documentos` // Usa em desenvolvimento para API local

  // Injeções de dependências
  private http = inject(HttpClient);
  private authService = inject(AuthService);

  constructor() {}

  private buildHeaders(tenantId: string): HttpHeaders {
    const token = this.authService.getToken();
    let headers = new HttpHeaders({ 'x-tenant-id': tenantId });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }
    return headers;

  }

  baixar(tenantId: string, id: number): Observable<Blob> {
  console.log('id :', id);
    const headers = this.buildHeaders(tenantId);
    return this.http.get(`${this.API_BASE}/${id}/download`, { headers, responseType: 'blob' });
  }
}
