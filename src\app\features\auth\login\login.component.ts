import { Component, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AuthService } from '../../../core/auth/services/auth.service';
import { LoginResponse } from '../../../shared/interfaces/auth';

@Component({
	selector: 'app-login',
	standalone: true,
	imports: [
		RouterModule,
		CommonModule,
		ReactiveFormsModule,
		MatCardModule,
		MatFormFieldModule,
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		MatProgressSpinnerModule,
	],
	templateUrl: './login.component.html',
	styleUrl: './login.component.scss',
})
export class LoginComponent {
	private formBuilder = inject(FormBuilder);
	private authService = inject(AuthService);
	private router = inject(Router);

	loginForm: FormGroup;
	carregando = signal(false);
	ocultaSenha = signal(true);
	menssagemErro = signal('');

	constructor() {
		this.loginForm = this.formBuilder.group({
			username: ['', [Validators.required, Validators.minLength(3)]],
			password: ['', [Validators.required, Validators.minLength(6)]],
		});
	}

	onSubmit(): void {
		if (this.loginForm.valid) {
			this.carregando.set(true);
			this.menssagemErro.set('');

			const { username, password } = this.loginForm.value;


			// Quando usar o identity remover essa validaçao
			if (this.authService.usaMock) {
				this.router.navigate(['/pagina-inicial']);
			}

			this.authService.login({ username, password }).subscribe({
				next: (response: LoginResponse) => {
					this.carregando.set(false);

					if (response.success && response?.result?.twoFactorEnabled) {
						this.router.navigate(['/confirmacao'], {
							queryParams: { username: username },
						});
					} else if (response.success && !response?.result?.twoFactorEnabled) {
						this.router.navigate(['/pagina-inicial']);
						
					} else {
						this.menssagemErro.set(response.result.message || 'Erro ao fazer login');
					}
				},
				error: (error: any) => {
					this.carregando.set(false);
					this.menssagemErro.set(error?.error?.message || 'Erro ao fazer login. Verifique suas credenciais.');
				},
			});
		}
	}
}
