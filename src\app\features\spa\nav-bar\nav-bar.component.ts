import { Component, output } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

import { NavLeftComponent } from './nav-left/nav-left.component';
import { NavRightComponent } from './nav-right/nav-right.component';

@Component({
  selector: 'app-nav-bar',
  imports: [NavLeftComponent, NavRightComponent, RouterModule, CommonModule],
  templateUrl: './nav-bar.component.html',
  styleUrls: ['./nav-bar.component.scss'],
})
export class NavBarComponent {
  readonly NavCollapsedMob = output();
  headerStyle: string;
  menuClass: boolean;
  collapseStyle: string;

  constructor() {
    this.headerStyle = '';
    this.menuClass = false;
    this.collapseStyle = 'none';
  }

  toggleMobOption() {
    this.menuClass = !this.menuClass;
    this.headerStyle = this.menuClass ? 'none' : '';
    this.collapseStyle = this.menuClass ? 'block' : 'none';
  }

  // this is for eslint rule
  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.closeMenu();
    }
  }

  closeMenu() {
    // Fecha o menu de configurações se estiver aberto
    if (this.menuClass) {
      this.menuClass = false;
      this.headerStyle = '';
      this.collapseStyle = 'none';
    }

    // Fecha o menu de navegação principal se estiver aberto
    const navbar = document.querySelector('app-navigation.pcoded-navbar');
    if (navbar && navbar.classList.contains('mob-open')) {
      // Emite o evento para o componente pai fechar o menu, centralizando o controle
      this.NavCollapsedMob.emit();
    }
  }
}
