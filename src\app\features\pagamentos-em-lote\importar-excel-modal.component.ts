import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PagamentosLotesService } from '../../core/services/api/pagamentos-lotes.service';

@Component({
  selector: 'app-importar-excel-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './importar-excel-modal.component.html',
  styleUrl: './importar-excel-modal.component.scss'
})
export class ImportarExcelModalComponent {
  private activeModal = inject(NgbActiveModal);
  private service = inject(PagamentosLotesService);

  arquivo = signal<File | null>(null);
  carregando = signal(false);
  erro = signal('');

  selecionar(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files && input.files[0] ? input.files[0] : null;
    this.arquivo.set(file);
  }

  enviar() {
    const f = this.arquivo();
    if (!f || !f.name.toLowerCase().endsWith('.xlsx')) { this.erro.set('Selecione um arquivo .xlsx'); return; }
    this.carregando.set(true);
    this.service.uploadExcel(f).subscribe({
      next: (r) => { this.carregando.set(false); this.activeModal.close({ sucesso: r.sucesso, idLote: r.idLote }); },
      error: () => { this.carregando.set(false); this.erro.set('Falha ao enviar o arquivo'); }
    });
  }

  fechar() { this.activeModal.close(); }
}

