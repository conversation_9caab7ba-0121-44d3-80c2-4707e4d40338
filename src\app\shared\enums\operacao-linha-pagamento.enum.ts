export enum OperacaoLinhaPagamento {
  Programar = 100,
  Liquidar = 200,
  <PERSON><PERSON><PERSON> = 300,
  Duplicado = 400,
  Erro = 500
}

export const operacaoLinhaOpcoes = () => [
  { valor: OperacaoLinhaPagamento.Programar, nome: 'PROGRAMAR' },
  { valor: OperacaoLinhaPagamento.Liquidar, nome: 'LIQUIDAR' },
  { valor: OperacaoLinhaPagamento.Ignorado, nome: 'IGNORADO' },
  { valor: OperacaoLinhaPagamento.Duplicado, nome: 'DUPLICADO' },
  { valor: OperacaoLinhaPagamento.Erro, nome: 'ERRO' }
];
