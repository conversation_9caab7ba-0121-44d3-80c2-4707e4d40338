// Estilos específicos para o componente de two-factor
.account-pages {
  min-height: 100vh;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card.bg-pattern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 0px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: none;
}

.auth-brand {
  margin-bottom: 1.5rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #e3e6f0;
  padding: 12px 16px;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0.5em;
  font-weight: 600;
  
  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
  
  &.is-invalid {
    border-color: #dc3545;
  }
}

.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  &.alert-success {
    background-color: #d4edda;
    color: #155724;
  }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-primary {
  color: #667eea !important;
  text-decoration: none;
  
  &:hover {
    color: #764ba2 !important;
    text-decoration: underline;
  }
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.text-muted {
  color: #6c757d !important;

  strong {
    
    font-weight: 600;
  }
}

// Responsividade
@media (max-width: 768px) {
  .account-pages {
    padding: 20px;
  }
  
  .col-md-8 {
    max-width: 100%;
  }
  
  .card-body {
    padding: 2rem 1.5rem !important;
  }
  
  .form-control {
    font-size: 18px; // Aumenta para melhor usabilidade mobile
  }
}

// Animações
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.5s ease-out;
}

// Estilo para o código de verificação
.form-control[type="text"] {
  font-family: 'Courier New', monospace;
}
