

export type TipoNotificacao = 'info' | 'aviso' | 'erro' | 'sucesso';

export interface DadosModalNotificacao {
  titulo: string;
  mensagem: string;
  tipo: TipoNotificacao;
  mostrarBotaoConfirmar?: boolean;
  textoConfirmar?: string;
  mostrarBotaoFechar?: boolean;
  textoFechar?: string;
  aoConfirmar?: () => void;
  aoFechar?: () => void;
  fecharAutomaticamente?: boolean;
  tempoFechoAutomatico?: number;
  permiteFecharClicandoFora?: boolean;
}

