<div class="auth-wrapper">
	<div class="auth-content">
		<div class="auth-bg">
			<div class="r from-top-right"></div>
			<div class="r s from-top-left"></div>
			<div class="r from-bottom-left"></div>
			<div class="r s from-bottom-right"></div>
		</div>
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					<img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
				</div>
				<h3 class="mb-4">Entrar</h3>
				<form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
					<div class="input-group mb-3">
						<input
							type="text"
							class="form-control"
							placeholder="Usuário"
							formControlName="username"
							[class.is-invalid]="loginForm.get('username')?.invalid && loginForm.get('username')?.touched"
						/>
						@if (loginForm.get('username')?.hasError('required') && loginForm.get('username')?.touched) {
							<div class="invalid-feedback">Usuário é obrigatório</div>
						}
						@if (loginForm.get('username')?.hasError('minlength') && loginForm.get('username')?.touched) {
							<div class="invalid-feedback">O usuário deve ter pelo menos 3 caracteres</div>
						}
					</div>
					<div class="input-group mb-4">
						<input
							[type]="ocultaSenha() ? 'password' : 'text'"
							class="form-control"
							placeholder="Password"
							formControlName="password"
							[class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
						/>
						<button class="btn btn-outline-secondary" type="button" (click)="ocultaSenha.set(!ocultaSenha())">
							<i [class]="ocultaSenha() ? 'feather icon-eye-off' : 'feather icon-eye'"></i>
						</button>
						@if (loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched) {
							<div class="invalid-feedback">Senha é obrigatória</div>
						}
						@if (loginForm.get('password')?.hasError('minlength') && loginForm.get('password')?.touched) {
							<div class="invalid-feedback">Senha deve ter pelo menos 6 caracteres</div>
						}
					</div>

					@if (menssagemErro()) {
						<div class="alert alert-danger">
							<i class="feather icon-alert-circle"></i>
							{{ menssagemErro() }}
						</div>
					}

					<!-- <div class="form-group text-start mb-4">
						<div class="checkbox checkbox-fill d-inline">
						<input type="checkbox" name="checkbox-fill-1" id="checkbox-fill-a1" />
						<label for="checkbox-fill-a1" class="cr">Save Details</label>
						</div>
					</div> -->

					<button type="submit" class="btn btn-primary mb-4" [disabled]="loginForm.invalid || carregando()">
						@if (!carregando()) {
							<span>Login</span>
						}
						@if (carregando()) {
							<span>
								<span class="spinner-border spinner-border-sm me-2" role="status"></span>
								Entrando...
							</span>
						}
					</button>
				</form>
				<p class="mb-2 text-muted">
					Esqueceu a senha?
					<a [routerLink]="['/recuperar-senha']">Recuperar</a>
				</p>
				<!-- <p class="mb-0 text-muted">
					Não tem uma conta?
					<a [routerLink]="['/cadastrar']">Criar</a>
				</p> -->
			</div>
		</div>
	</div>
</div>
