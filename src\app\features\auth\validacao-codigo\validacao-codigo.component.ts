import { Component, OnInit, OnDestroy, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
	selector: 'app-validacao-codigo',
	standalone: true,
	imports: [CommonModule, ReactiveFormsModule],
	templateUrl: './validacao-codigo.component.html',
	styleUrls: ['./validacao-codigo.component.scss'],
})
export class ValidacaoCodigoComponent implements OnInit, OnDestroy {
	private fb = inject(FormBuilder);
	private router = inject(Router);
	private route = inject(ActivatedRoute);

	doisFatForm: FormGroup;
	carregando = signal(false);

    erroEnvioEmail = signal(false);
    mensagemEnvioEmail = signal('Digite o código de 6 dígitos enviado para no seu e-mail');
    erroVerificaCodigo = signal(false);
    mensagemVerificaCodigo = signal('');

	parametro: string | null = null;

	constructor() {

		this.router.url.includes('atualizacao-status-veiculo') ? this.parametro = '/atualizacao-status-veiculo' : this.parametro = '/pagamento-ressarcimento';

		this.doisFatForm = this.fb.group({
			codigo: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
		});
	}

	ngOnInit(): void {
	}

	ngOnDestroy(): void {
	}

	onSubmit(): void {
		if (this.doisFatForm.valid && !this.carregando()) {
			this.carregando.set(true);
			this.erroVerificaCodigo.set(false);
            this.mensagemVerificaCodigo.set('');

            const valor = this.route.snapshot.queryParamMap.get('valor');
			const idAgregador = this.route.snapshot.queryParamMap.get('idAgregador');
			const idAtivo = this.route.snapshot.queryParamMap.get('idAtivo');
			
			this.router.navigate([this.parametro], {
				queryParams: { valor , idAgregador, idAtivo },
			});

		}
	}

	

}
