<div class="card" style="font-family: 'Open Sans', sans-serif">
	<div class="card-header">
		<div class="d-flex justify-content-between align-items-center">
			<h5 class="card-title mb-0">Consolidado por Periodo</h5>
		</div>
	</div>
<div class="card-body">


<div class="select-periodo">
	<label for="periodo">Período:</label>
	<input type="date" id="dateDefault" class="form-control" placeholder="Date" />
	<label for="periodo"> até </label>
	<input type="date" id="dateDefault" class="form-control" placeholder="Date" />

</div>

<div class="dashboard-primeira-linha">
	<div class="card-principal">
		<div class="dashboard-card modern-card-autorizados">
			<div class="header-itens">
				<div class="modern-header">
					<span class="modern-icon">
						<i class="fa fa-check-circle"></i>
					</span>
				</div>
				<div class="modern-value-column">
					<span class="modern-title-autorizado">Autorizado Bruto</span>
					<span class="modern-value">{{ painel?.autorizado?.total| currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</span>
				</div>
			</div>
		</div>

		<div class="composto" style="display: flex; flex-direction: column">
			<div class="composto-itens">
				<div class="composto-item tres exclusao" 
					(click)="direcionarParaMovimentacoes('exclusaoAntesPagamento')"
					(mouseenter)="cardHoveredPagoAntes = true" 
					(mouseleave)="cardHoveredPagoAntes = false">
					<span class="data-title color-label">Exclusão Antes do Pagamento</span>
					<span class="data-valor color-label"
						><b>{{ painel?.autorizado?.exclusaoAntesPagamento?.valor| currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
				<div class="composto-item tres pago-sem-exclusao" 
					(click)="direcionarParaMovimentacoes('pagoSemExclusao')"
					(mouseenter)="cardHoveredPagoSem = true" 
					(mouseleave)="cardHoveredPagoSem = false">
					<span class="data-title color-label">Pago Sem Exclusão</span>
					<span class="data-valor color-label"
						><b>{{ painel?.autorizado?.pagoSemExclusao?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
				<div class="composto-item tres pago-com-exclusao"
					(click)="direcionarParaRessarcimentos('')"
					(mouseenter)="cardHovered = true" 
					(mouseleave)="cardHovered = false">
					<span class="data-title color-label">Pago Com Exclusão</span>
					<span class="data-valor color-label"
						><b>{{ painel?.autorizado?.pagoComExclusao?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
				<div class="composto-item tres apagar"
					(click)="direcionarParaPagamentos('pagar')"
					[class.hover-effect-success]="cardHoveredPagar"
					(mouseenter)="cardHoveredPagar = true" 
					(mouseleave)="cardHoveredPagar = false">
					<span class="data-title color-label">A Pagar</span>
					<span class="data-valor color-label"
						><b>{{ painel?.autorizado?.pagar?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
			</div>

			<div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-top: 12px">
				<div class="modern-pie-chart-container">
					<svg viewBox="0 0 120 120" width="160" height="160">
						<circle
							class="pie-chart-exclusao"
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel?.autorizado?.exclusaoAntesPagamento?.porcentagem)"
							stroke-dashoffset="0"
						/>
						<circle
							class="pie-chart-pago-com-exclusao"
							
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel?.autorizado?.pagoComExclusao?.porcentagem)"
							[attr.stroke-dashoffset]="getStrokeDashoffset(painel?.autorizado?.exclusaoAntesPagamento?.porcentagem)"
						/>
						<circle
							class="pie-chart-pago-sem-exclusao"
							r="50"
							cx="60"
							cy="60"
							stroke="#92d3df"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel?.autorizado?.pagoSemExclusao?.porcentagem)"
							[attr.stroke-dashoffset]="getStrokeDashoffset((painel?.autorizado?.exclusaoAntesPagamento?.porcentagem ?? 0) + (painel?.autorizado?.pagoComExclusao?.porcentagem ?? 0))"
						/>
						<circle
							class="pie-chart-apagar"
							
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel?.autorizado?.pagar?.porcentagem)"
							[attr.stroke-dashoffset]="getStrokeDashoffset((painel?.autorizado?.exclusaoAntesPagamento?.porcentagem ?? 0) + (painel?.autorizado?.pagoComExclusao?.porcentagem ?? 0) + (painel?.autorizado?.pagoSemExclusao?.porcentagem ?? 0))"
						/>
					</svg>
					<div class="pie-chart-field-legend">
						<div class="pie-chart-legend">
							<span class="pie-chart-icon-legend exclusao" [class.hover-effect]="cardHoveredPagoAntes"></span>
							Exclusão ({{ painel.autorizado?.exclusaoAntesPagamento?.porcentagem | number: '1.2-5' : 'pt-BR' }}%)
						</div>
						<div class="pie-chart-legend">
							<span class="pie-chart-icon-legend pago-sem-exclusao" [class.hover-effect]="cardHoveredPagoSem"></span>
							Pago Sem Exclusão({{ painel.autorizado?.pagoSemExclusao?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
						<div class="pie-chart-legend">
							<span class="pie-chart-icon-legend pago-com-exclusao" [class.hover-effect]="cardHovered"></span>
							Pago Com Exclusão ({{ painel.autorizado?.pagoComExclusao?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
						<div class="pie-chart-legend" >
							<span class="pie-chart-icon-legend apagar" [class.hover-effect]="cardHoveredPagar || cardHoveredPagarIncluidas || cardHoveredPagarPendentes"></span>
							A pagar ({{ painel.autorizado?.pagar?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="card-principal-pagar" [class.hover-effect]="cardHoveredPagar">
		<div class="dashboard-card modern-card-pagar">
			<div class="header-itens">
				<div class="modern-header">
					<span class="modern-icon">
						<i class="fa fa-hand-holding-usd"></i>
					</span>
				</div>
				<div class="modern-value-column">
					<span class="modern-title-autorizado">A pagar</span>
					<span class="modern-value">{{ painel.pagar?.total | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</span>
				</div>
			</div>
		</div>

		<div class="composto" style="display: flex; flex-direction: column">
			<div class="composto-itens">
				<div class="composto-item dois incluidas" 
					(click)="direcionarParaPagamentos('nfsIncluidas')"
					(mouseenter)="cardHoveredPagarIncluidas = true" 
					(mouseleave)="cardHoveredPagarIncluidas = false">

					<span class="data-title color-label">NFs Incluídas</span>
					<span class="data-valor color-label"
						><b>{{ painel.pagar?.nfsIncluidas?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
				<div class="composto-item dois nfpendentes" 
					(click)="direcionarParaPagamentos('nfsPendentes')"
					(mouseenter)="cardHoveredPagarPendentes = true" 
					(mouseleave)="cardHoveredPagarPendentes = false">
					<span class="data-title color-label">NFs Pendentes </span>
					<span class="data-valor color-label"
						><b>{{ painel.pagar?.nfsPendentes?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
			</div>

			<div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-top: 12px">
				<div class="modern-pie-chart-container">
					<svg viewBox="0 0 120 120" width="160" height="160">
						<circle
							class="pie-chart-incluidas"
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel.pagar?.nfsIncluidas?.porcentagem)"
							stroke-dashoffset="0"
						/>
						<circle
							class="pie-chart-pendentes"
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel.pagar?.nfsPendentes?.porcentagem)"
							[attr.stroke-dashoffset]="getStrokeDashoffset(painel.pagar?.nfsIncluidas?.porcentagem)"
						/>
					</svg>
					<div class="pie-chart-legend">
						<div>
							<span class="pie-chart-icon-legend incluidas"></span>
							Incluídas ({{ painel.pagar?.nfsIncluidas?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
						<div>
							<span class="pie-chart-icon-legend pendentes"></span>
							Pendentes ({{ painel.pagar?.nfsPendentes?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="card-principal-ressarcimento" [class.hover-effect]="cardHovered">
		<div class="dashboard-card modern-card-ressarcimento">
			<div class="header-itens">
				<div class="modern-header">
					<span class="modern-icon">
						<i class="fa fa-undo"></i>
					</span>
				</div>
				<div class="modern-value-column">
					<span class="modern-title-autorizado">Ressarcimento</span>
					<span class="modern-value">{{ painel?.ressarcimento?.total | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</span>
					<!-- <span class="modern-up"><i class="fa fa-arrow-up"></i></span> -->
				</div>
			</div>
		</div>

		<div class="composto" style="display: flex; flex-direction: column">
			<div class="composto-itens">
				<div class="composto-item dois realizados" (click)="direcionarParaRessarcimentos('valorRessarcido')">
					<span class="data-title color-label">Realizados</span>
					<span class="data-valor color-label"
						><b>{{ painel.ressarcimento?.realizados?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
				<div class="composto-item dois pendentes" (click)="direcionarParaRessarcimentos('valorRessarcir')">
					<span class="data-title color-label">Pendentes</span>
					<span class="data-valor color-label"
						><b>{{ painel.ressarcimento?.pendentes?.valor | currency: 'BRL' : 'symbol' : '1.2-2' : 'pt-BR' }}</b></span
					>
				</div>
			</div>

			<div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-top: 12px">
				<div class="modern-pie-chart-container">
					<svg viewBox="0 0 120 120" width="160" height="160">
						<circle
							class="pie-chart-realizados"
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke="#746a30"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel.ressarcimento?.realizados?.porcentagem)"
							stroke-dashoffset="0"
						/>
						<circle
							class="pie-chart-ressarcimentos-pendentes"
							r="50"
							cx="60"
							cy="60"
							fill="transparent"
							stroke="#b6ac26"
							stroke-width="18"
							[attr.stroke-dasharray]="getStrokeDasharray(painel.ressarcimento?.pendentes?.porcentagem)"
							[attr.stroke-dashoffset]="getStrokeDashoffset(painel.ressarcimento?.realizados?.porcentagem)"
						/>
					</svg>
					<div class="pie-chart-legend">
						<div>
							<span class="pie-chart-icon-legend realizados"></span>
							Realizados ({{ painel.ressarcimento?.realizados?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
						<div>
							<span class="pie-chart-icon-legend ressarcimentos-pendentes"></span>
							Pendentes ({{ painel.ressarcimento?.pendentes?.porcentagem | number: '1.2-2' : 'pt-BR' }}%)
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

</div>
</div>