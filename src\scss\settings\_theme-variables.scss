@import 'src/scss/settings/bootstrap-variables';
// =======================================
//     List of variables for layout
// =======================================
:root {
  // body
  --#{$prefix}body-bg: #{$body-bg};
  --bs-body-bg-rgb: #{to-rgb($body-bg)};

  --pc-heading-color: #{$gray-800};
  --pc-active-background: #{$gray-200};

  // Navbar
  --pc-sidebar-background: #3f4d67;
  --pc-sidebar-color: #a9b7d0;
  --pc-sidebar-color-rgb: #{to-rgb(#a9b7d0)};
  --pc-sidebar-active-color: #{$primary};
  --pc-sidebar-active-background: rgba(0, 0, 0, 0.1);
  --pc-sidebar-main-active-color: #fff;
  --pc-sidebar-shadow: 1px 0 20px 0 #3f4d67;
  --pc-sidebar-caption-color: #e8edf7;
  --pc-sidebar-border: none;

  // header
  --pc-header-background: rgba(#{var(--bs-body-bg-rgb)}, 0.7);
  --pc-header-color: #{$gray-600};
  --pc-header-shadow: none;

  // card
  --pc-card-box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
  --bs-card-color: #{$gray-600};

  // horizontal menu
  --pc-header-submenu-background: #{$white};
  --pc-header-submenu-color: #{$gray-600};

  // table
  --pc-table-header-background: #{$gray-100};
  --pc-table-header-background-150: #{$gray-150};
  --pc-table-header-background-300: #{$gray-300};
  --pc-table-background: #{$white};

  // text
  --pc-text-color: #{$gray-600};
}

$header-height: 70px;
$Menu-width: 264px;
$Menu-collapsed-width: 80px;

// horizontal menu
$topbar-height: 70px;

$soft-bg-level: -80%;
// =====================================
//      Variables for dark layouts
// =====================================
$dark-layout-color: #212224;

// =====================================
//      Variables for bootstrap color
// =====================================

$blue: $blue-500;
$secondary: $gray-600;
$indigo: $indigo-500;
$purple: $purple-500;
$pink: $pink-500;
$red: $red-500;
$orange: $orange-500;
$yellow: $yellow-500;
$green: $green-500;
$teal: $teal-500;
$cyan: $cyan-500;

$primary-text: $blue-600;
$secondary-text: $gray-600;
$success-text: $green-600;
$info-text: $cyan-700;
$warning-text: $yellow-700;
$danger-text: $red-600;
$light-text: $gray-600;
$dark-text: $gray-700;

$primary-bg-subtle: $blue-100;
$secondary-bg-subtle: $gray-100;
$success-bg-subtle: $green-100;
$info-bg-subtle: $cyan-100;
$warning-bg-subtle: $yellow-100;
$danger-bg-subtle: $red-100;
$light-bg-subtle: mix($gray-100, $white);
$dark-bg-subtle: $gray-400;

$primary-border-subtle: $blue-200;
$secondary-border-subtle: $gray-200;
$success-border-subtle: $green-200;
$info-border-subtle: $cyan-200;
$warning-border-subtle: $yellow-200;
$danger-border-subtle: $red-200;
$light-border-subtle: $gray-200;
$dark-border-subtle: $gray-500;
$brand-colors: (
  'brand-color-1': $brand-color1,
  'brand-color-2': $brand-color2,
  'brand-color-3': $brand-color3
);
// $preset-colors: (
//   preset-1: (
//     primary: $blue-500
//   ),
//   preset-2: (
//     primary: $indigo-500
//   ),
//   preset-3: (
//     primary: $purple-500
//   ),
//   preset-4: (
//     primary: $pink-500
//   ),
//   preset-5: (
//     primary: $red-500
//   ),
//   preset-6: (
//     primary: $orange-500
//   ),
//   preset-7: (
//     primary: $yellow-500
//   ),
//   preset-8: (
//     primary: $green-500
//   ),
//   preset-9: (
//     primary: $teal-500
//   ),
//   preset-10: (
//     primary: $cyan-500
//   ),
//   preset-11: (
//     primary: $dark
//   )
// );
