import { Component, EventEmitter, Input, Output, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, GridReadyEvent, RowClickedEvent, AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { ThemeService } from '../../../services/theme.service';
import { inject, effect } from '@angular/core';
import { criarConfigTema } from '../tabela-config-tema';
import { AgGridLocaleText } from '../../../enums/tabelas/ag-grid-locale.enum';

// Registrar os módulos AG Grid
ModuleRegistry.registerModules([AllCommunityModule]);

@Component({
	selector: 'app-tabela-padrao',
	standalone: true,
	imports: [CommonModule, AgGridAngular],
	templateUrl: './tabela-padrao.component.html',
	styleUrl: './tabela-padrao.component.scss',
	encapsulation: ViewEncapsulation.None,
})
export class TabelaPadraoComponent implements OnInit {
	// Entradas para configuração
	@Input() columnDefs: ColDef[] = [];
	@Input() rowData: any[] = [];
	@Input() height = 'calc(100vh - 311px)';
	@Input() tooltipShowDelay = 500;
	@Input() defaultColDef: ColDef = {
		flex: 1,
		minWidth: 80,
		filter: 'agTextColumnFilter',
		suppressHeaderMenuButton: true,
		suppressHeaderContextMenu: true,
	};
	@Input() tooltipHideDelay: number = 10000;
	@Input() pagination = true;
	@Input() paginationPageSize = 10;
	@Input() paginationAutoPageSize = false;
	@Input() rowSelection: 'single' | 'multiple' = 'single';
	@Input() enableRtl = false;
	@Input() suppressRowClickSelection = true;
	@Input() enableCellTextSelection = true;
	@Input() rowHeight = 40;
	@Input() headerHeight = 45;
	@Input() domLayout: 'normal' | 'autoHeight' | 'print' = 'normal';
	@Input() rowModelType: any = 'clientSide';

	// Eventos de saída
	@Output() rowClicked = new EventEmitter<any>();
	@Output() gridReady = new EventEmitter<GridReadyEvent>();
	@Output() cellClicked = new EventEmitter<any>();
	@Output() rowSelected = new EventEmitter<any>();
	@Output() sortChanged = new EventEmitter<any>();

	get processedColumnDefs(): ColDef[] {
		return this.columnDefs.map((col) => {
			if (col.cellRenderer && !col.cellStyle) {
				return {
					...col,
					cellStyle: {
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						height: '100%',
						cursor: 'pointer',
						fontSize: '16px',
					},
				};
			}
			return col;
		});
	}

	// defaultColDef: ColDef = {
	// 	flex: 1,
	// 	minWidth: 80,
	// 	filter: 'agTextColumnFilter',
	// 	suppressHeaderMenuButton: true,
	// 	suppressHeaderContextMenu: true,
	// };

	localeText = AgGridLocaleText;

	private themeService = inject(ThemeService);

	// Estado interno
	isDarkTheme = false;
	showGrid = true;
	tema = criarConfigTema(this.themeService.isDarkTheme());

	constructor() {
		// Monitora mudanças de tema usando o effect
		effect(() => {
			this.isDarkTheme = this.themeService.isDarkTheme() === 'dark';
			this.enableRtl = this.themeService.isRtlTheme();
		});
	}

	ngOnInit(): void {
		this.isDarkTheme = this.themeService.isDarkTheme() === 'dark';
		this.enableRtl = this.themeService.isRtlTheme();
	}

	onGridReady(params: GridReadyEvent): void {
		this.gridReady.emit(params);
	}

	onRowClicked(event: RowClickedEvent): void {
		this.rowClicked.emit(event);
	}

	onCellClicked(event: any): void {
		this.cellClicked.emit(event);
	}

	onRowSelected(event: any): void {
		this.rowSelected.emit(event);
	}

	onSortChanged(event: any): void {
		this.sortChanged.emit(event);
	}
}
