.dashboard-container {
  height: 100vh;
  overflow: hidden;
}

.sidenav-container {
  height: 100%;
}

.sidenav {
  width: 250px;
  transition: width 0.3s ease;
}

.sidenav.collapsed {
  width: 64px;
}

.sidenav-header {
  background-color: #3f51b5;
  color: white;
  padding: 0 16px;
  min-height: 64px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.logo.collapsed {
  justify-content: center;
}

.logo-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
}

.logo-text {
  transition: opacity 0.3s ease;
}

.nav-list {
  padding-top: 0;
}

.nav-item {
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background-color: rgba(63, 81, 181, 0.1);
}

.nav-item.collapsed {
  width: 48px;
  margin: 4px 8px;
  justify-content: center;
}

.top-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-spacer {
  flex: 1 1 auto;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-size: 0.9rem;
  color: white;
}

.main-content {
  background-color: #f5f5f5;
}

.content-area {
  padding: 24px;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.welcome-section h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 400;
}

.welcome-section p {
  margin: 0 0 32px 0;
  color: #666;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon mat-icon {
  font-size: 24px;
  height: 24px;
  width: 24px;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .user-name {
    display: none;
  }

  .content-area {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
