.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card-wrapper {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
}

.logo-icon {
  font-size: 2rem;
  height: 2rem;
  width: 2rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.full-width {
  width: 100%;
}

.login-button {
  height: 48px;
  font-size: 1rem;
  margin-top: 1rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f44336;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.error-message mat-icon {
  font-size: 1.2rem;
  height: 1.2rem;
  width: 1.2rem;
}

mat-card-subtitle {
  color: #666;
  text-align: center;
}

mat-spinner {
  margin-right: 8px;
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-card {
    padding: 1.5rem;
  }
}
