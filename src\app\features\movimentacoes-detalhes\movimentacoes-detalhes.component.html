<div class="card" #toAnimate>
	<div class="card-header">
		<div class="d-flex justify-content-between align-items-center">
			<h5 class="card-title mb-0">Detalhes da Movimentação</h5>
			<div class="action-buttons">
				<div class="d-flex">
					<div class="text-muted p-2 d-flex align-items-center">Sinistro:</div>
					<div class="info-label align-content-center ml-2" *ngIf="infoAgregador() && infoAgregador()?.numero">
						{{ infoAgregador()?.numero || '-' }}
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="card-body mt-0 pt-0">
		<!-- Bloco de informações do agregador, veículo e fornecedor -->

		<div class="row p-2">
			<div class="col-md-6 col-xl-3 p-1">
				<div class="info-card">
					<div class="d-flex flex-column justify-content-between h-100">
						<h6 class="titulo-card"><b>Veículo</b></h6>
						@if (isLoading()) {
							<div class="d-flex justify-content-center">
								<div class="spinner-border" role="status">
									<span class="sr-only"></span>
								</div>
							</div>
						} @else {
							<div class="w-100 text-muted f-12">
								@if (infoAtivo()) {
									<div class="col-12">
										<div class="mb-0">Placa: {{ infoAtivo()?.placa || '-' }}</div>
										<div class="mb-0">Modelo: {{ infoAtivo()?.modelo || '-' }}</div>
										<div class="column d-flex space-between gap-4">
											<div class="mb-0">Ano: {{ infoAtivo()?.ano || '-' }}</div>
											<div class="mb-0">Cor: {{ infoAtivo()?.cor || '-' }}</div>
										</div>
									</div>
								} @else {
									<div class="text-center">
										<span>Nenhum veículo encontrado</span>
									</div>
								}
							</div>
							<div class="text-center mt-0">
								<div class="badge mt-2 bg-secondary f-12">{{ infoAtivo()?.situacaoReparo }}</div>
							</div>
						}
					</div>
				</div>
				<div class="position-relative">
					<i class="fa-solid fa-car-side icone-transparente-detalhes mb-2"></i>
				</div>
			</div>

			<div class="col-md-6 col-xl-3 p-1">
				<div class="info-card">
					<div class="d-flex flex-column justify-content-between h-100">
						<h6 class="titulo-card"><b>Oficina</b></h6>
						@if (isLoading()) {
							<div class="d-flex justify-content-center">
								<div class="spinner-border" role="status">
									<span class="sr-only"></span>
								</div>
							</div>
						} @else {
							<div class="w-100 text-muted f-12">
								@if (infoOficina()) {
									<div class="mb-1">{{ infoOficina().nome || '-' }}</div>
									<div class="mb-1">{{ (infoOficina().cnpj || '-') | cnpj }}</div>
								} @else {
									<div class="text-center">
										<span>Nenhuma oficina encontrada</span>
									</div>
								}
							</div>
							<div class="text-center f-12">
								@if (infoOficina()) {
									<button class="btn btn-outline-primary btn-sm mt-2" (click)="solicitarStatusOficina()">
										<i class="fa-solid fa-wrench"></i>
										Solicitar Status do Veículo
									</button>
								}
							</div>
						}
					</div>
				</div>
				<div class="position-relative">
					<i class="fa-solid fa-wrench icone-transparente-detalhes mb-2"></i>
				</div>
			</div>

			<div class="col-md-6 col-xl-3 p-1">
				<div class="info-card">
					<div class="d-flex flex-column h-100">
						<h6 class="titulo-card"><b>Fornecedor</b></h6>
						@if (isLoading()) {
							<div class="d-flex justify-content-center">
								<div class="spinner-border" role="status">
									<span class="sr-only"></span>
								</div>
							</div>
						} @else {
							<div class="w-100 text-muted mt-1 f-12">
								@if (infoOficina()) {
									<div class="mb-1">{{ infoFornecedor()?.nomeFantasia }}</div>
									<div class="mb-1">{{ infoFornecedor()?.razaoSocial }}</div>
									<div class="mb-1">{{ (infoFornecedor()?.cnpj || '-') | cnpj }}</div>
								} @else {
									<div class="text-center pt-4">
										<span>Nenhum fornecedor encontrado</span>
									</div>
								}
							</div>
						}
					</div>
				</div>
				<div class="position-relative">
					<i class="fa-solid fa-briefcase icone-transparente-detalhes mb-2"></i>
				</div>
			</div>

			<div class="col-md-6 col-xl-3 p-1">
				<div class="info-card">
					<div class="d-flex flex-column justify-content-between h-100">
						<div class="titulo-card"><b>Dados Bancários (Fornecedor):</b></div>
						@if (isLoading()) {
							<div class="d-flex justify-content-center">
								<div class="spinner-border" role="status">
									<span class="sr-only"></span>
								</div>
							</div>
						} @else {
							<div class="w-100 text-muted f-12 mt-1">
								@if (infoFornecedor()?.dadosBancarios || infoFornecedor()?.agencia) {
									<div class="mb-0">
										{{ infoFornecedor()?.dadosBancarios?.banco || '-' }} - {{ infoFornecedor()?.dadosBancarios?.codigoBanco }}
									</div>
									<div class="mb-0">Agência: {{ infoFornecedor()?.dadosBancarios?.agencia }}</div>
									<div class="d-flex gap-4">
										Conta: {{ infoFornecedor()?.dadosBancarios?.conta }}
										<div class="">Tipo: {{ infoFornecedor()?.dadosBancarios?.tipoConta }}</div>
									</div>
								} @else {
									<div class="text-center">
										<span>Nenhum dado bancário encontrado</span>
									</div>
								}
								<div class="text-center f-12">
									<button class="btn btn-outline-primary btn-sm mt-2" (click)="solicitarDadosBancarios()">
										<i class="fa-solid fa-building-columns"></i>
										Solicitar Dados Bancários
									</button>
								</div>
							</div>
						}
					</div>
				</div>
				<div class="position-relative">
					<i class="fa-solid fa-building-columns icone-transparente-detalhes mb-2"></i>
				</div>
			</div>
		</div>

		<hr class="linha mt-0" />

		<div class="row mt-0">
			@for (valor of valoresCards; track $index) {
				<div class="col-xxl-3 col-md-6 m-0">
					<div class="info-card  {{ valor.background }}">
						<div class="d-flex align-items-center">
							<div class="flex-grow-1">
								<h4 class="mb-1 text-white">
									{{ valor.valor | currency: 'BRL' : 'symbol-narrow' : '1.2-2' : 'pt-BR' }}
								</h4>
								<div class="column">
									<p class="mb-1 fw-medium text-white">{{ valor.titulo }}</p>
									@if (valor.titulo === 'Total A Ressarcir' && valor.valor > 0) {
										<div class="btn-ressarcimento">
											<button class="bg-white border-0" (click)="solicitarRessarcimento()">
												<i class="fa-solid fa-undo"></i>
												Solicitar Ressarcimento
											</button>
										</div>
									}
								</div>
							</div>

							<div class="flex-shrink-0 z-index-0">
								<i class="{{ valor.icon }} f-28 icone-transparente text-white"></i>
							</div>
						</div>
					</div>
				</div>
			}
		</div>

		<div class="col-sm-12 mt-4">
			<div ngbAccordion>
				<div ngbAccordionItem class="info-card-border" [collapsed]="true">
					<h3 ngbAccordionHeader>
						<button ngbAccordionButton class="f-18">
							<div class="d-flex align-items-center gap-2 text-muted">
								<i class="fa fa-calendar"></i>
								<span>Histórico (Pagamentos/Ressarcimentos)</span>
							</div>
						</button>
					</h3>
					<div ngbAccordionCollapse>
						<div ngbAccordionBody>
							<ng-template>
								<div class="row col-12">
									@if (isLoading()) {
										<div class="d-flex justify-content-center">
											<div class="spinner-border" role="status">
												<span class="sr-only"></span>
											</div>
										</div>
									} @else {
										<div class="mt-4 col-md-6">
											<h4 class="mb-2 text-muted f-16"><i class="fa fa-dollar"></i> Pagamentos</h4>
											<app-tabela-expansiva
												[dadosTabelaPrincipal]="dadosHistoricoPagamentos()"
												[colunasTabelaPrincipal]="colunasHistoricosPagamentos"
												[colunasTabelaExpansao]="colunasHistoricosExpansaoPagamentos"
												[propriedadeDadosExpansao]="'liquidacoes'"
											></app-tabela-expansiva>
										</div>

										<div class="mt-4 col-md-6">
											<h4 class="mb-2 text-muted f-16"><i class="fa fa-undo"></i> Ressarcimentos</h4>
											<app-tabela-expansiva
												[dadosTabelaPrincipal]="dadosHistoricoRessarcimentos()"
												[colunasTabelaPrincipal]="colunasHistoricosRessarcimentos"
												[colunasTabelaExpansao]="colunasHistoricosExpansaoRessarcimentos"
												[propriedadeDadosExpansao]="'liquidacoes'"
											></app-tabela-expansiva>
										</div>
									}
								</div>
							</ng-template>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="col-12 mt-4">
			<div class="col-12 col-md-12 mb-3">
				<div ngbAccordion [closeOthers]="true">
					<div class="info-card-border" ngbAccordionItem id="multiCollapseExample1" [collapsed]="false">
						<h3 ngbAccordionHeader>
							<button ngbAccordionButton class="f-18" aria-controls="multiCollapseExample1">
								<div class="d-flex align-items-center gap-2 text-muted">
									<i class="fa fa-file"></i>
									<span>Documentos</span>
								</div>
							</button>
						</h3>
						<div ngbAccordionCollapse>
							@if (isLoading()) {
								<div class="d-flex justify-content-center p-3">
									<div class="spinner-border" role="status">
										<span class="sr-only"></span>
									</div>
								</div>
							} @else {
								<div class="row" ngbAccordionBody>
									@for (list of infoDocumentos(); track list.id) {
										@if (list) {
											<div class="col-xl-6 card overflow-hidden mb-2 rounded shadow-none">
												<div
													class="card-body px-3 py-2 border-start border-4 
													{{
														list?.tipo.codigo === documentoTipo.NotaFiscalVenda ? 'border-success' : 
														list?.tipo.codigo === documentoTipo.NotaFiscalDevolucao ? 'border-purple' :
														list?.tipo.codigo === documentoTipo.NotaFiscalServico ? 'border-warning' : 
														list?.tipo.codigo === documentoTipo.Recibo ? 'border-primary' : 
														'border-default'
													}} 
													border-doc"
												>
												<div class="d-flex justify-content-between align-items-start mb-0">
													<a class="feather icon-download text-primary nota-icon" placement="top" ngbTooltip="Baixar Documento" container="body" (click)="baixarDocumento(list.id)"></a>
													<h6 class="text-muted f-16"> <b>{{ list?.tipo?.descricao || '-' }}</b> </h6>
													<p class="">R$ {{ (list?.codigo | currency: 'BRL' : 'symbol-narrow') || 0 }}</p>
												</div>

												<div class="d-flex column col-12">
													<div class="col-6">
														<p class="mb-0">Nº: {{ list?.numero || '-' }}</p>
														<p class="mb-0">Série: {{ list?.serie || '-' }}</p>
														@if (list?.chave) {
															<p class="mb-0"><b>Chave:</b> {{ list?.chave || '-' }}</p>
														}
													</div>
													<div class="d-flex col-6 align-items-end justify-content-end pb-">
														<p class="mb-0">Data Emissão: {{ (list?.dataEmissao | date: 'dd/MM/yyyy') || '-' }}</p>

													</div>														

												</div>

											</div>
											</div>
										}
									} @empty {
										<span class="text-center">Nenhum documento encontrado</span>
									}
								</div>
							}
						</div>
					</div>
				</div>
			</div>

			<div class="col-12 pt-2 pb-2">
				<div class="info-card-border p-3">
					<h4 class="mb-2 text-muted">Itens do Sinistro</h4>

					@if (isLoading()) {
						<div class="d-flex justify-content-center p-3">
							<div class="spinner-border" role="status">
								<span class="sr-only"></span>
							</div>
						</div>
					} @else {
						<app-tabela-expansiva
							[dadosTabelaPrincipal]="dadosTabelaItens"
							[colunasTabelaPrincipal]="colunasTabelaItens"
							[colunasTabelaExpansao]="colunasTabelaItensExpansao"
						></app-tabela-expansiva>
					}
				</div>
			</div>

			<hr class="linha" />

			<div class="col-sm-12 mt-4">
				<div ngbAccordion>
					<div ngbAccordionItem class="info-card-border" [collapsed]="true">
						<h3 ngbAccordionHeader>
							<button ngbAccordionButton class="f-18">
								<div class="d-flex align-items-center gap-2 text-muted">
									<i class="fa fa-database"></i>
									<span>Todos os Itens do Sinistro</span>
								</div>
							</button>
						</h3>
						<div ngbAccordionCollapse>
							<div ngbAccordionBody>
								<ng-template>
									@if (isLoading()) {
										<div class="d-flex justify-content-center p-3">
											<div class="spinner-border" role="status">
												<span class="sr-only"></span>
											</div>
										</div>
									} @else {
										<app-tabela-expansiva
											[dadosTabelaPrincipal]="dadosOutrosItens"
											[colunasTabelaPrincipal]="colunasOutrosItens"
											[colunasTabelaExpansao]="colunasOutrosItensExpansao"
										></app-tabela-expansiva>
									}
								</ng-template>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
