export enum DocumentoTipo {

    Desconhecido = 0,
    NotaFiscalVenda = 100,
    NotaFiscalDevolucao = 110,
    NotaFiscalServico = 200,
    Recibo = 300,
    Outro = 900
}

export function getDocumentoTipoDescricao(tipo: DocumentoTipo): string {
    switch (tipo) {
        case DocumentoTipo.Desconhecido:
            return 'Desconhecido';
        case DocumentoTipo.NotaFiscalVenda:
            return 'Nota Fiscal de Venda';
        case DocumentoTipo.NotaFiscalDevolucao:
            return 'Nota Fiscal de Devolução';
        case DocumentoTipo.NotaFiscalServico:
            return 'Nota Fiscal de Serviço';
        case DocumentoTipo.Recibo:
            return 'Recibo';
        case DocumentoTipo.Outro:
            return 'Outro';
        default:
            return 'Desconhecido';
    }
}
