import { Component, OnInit, ChangeDetectorRef, Provider } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { RegrasProcessamentoService } from '../../core/services/api/regras-processamento.service';
import { ConfiguracaoProcessamento } from '../../shared/interfaces/api/regras-processamento.interface';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { StatusVeiculo } from '../../shared/enums/status-veiculo.enum';
import { AuthService } from '../../core/auth/services/auth.service';
import { SelectSimplesComponent } from '../../shared/components/selects/select-simples/select-simples.component';
import { Tipagem } from '../../shared/enums/tipagem.enum';
import { TipoProcessamento } from '../../shared/enums/tipo-processamento.enum';
import { TipoConfiguracao } from '../../shared/enums/tipo-configuracao.enum';
import { Operador } from '../../shared/enums/operador.enum';
import { DialogoService } from '../../core/services/dialogos/dialogo.service';

const RegrasProcessamentoServiceProvider: Provider = {
	provide: RegrasProcessamentoService,
};

@Component({
	selector: 'app-regras-processamento',
	templateUrl: './regras-processamento.component.html',
	styleUrls: ['./regras-processamento.component.scss'],
	standalone: true,
	imports: [CommonModule, ReactiveFormsModule, CardComponent, SelectSimplesComponent],
	providers: [RegrasProcessamentoServiceProvider],
})
export class RegrasProcessamentoComponent implements OnInit {
	regrasForm!: FormGroup;
	configuracoes: ConfiguracaoProcessamento[] = [];
	editando = false;
	idConfiguracaoEmEdicao: string | null = null;
	tipagemAtual: Tipagem = Tipagem.NUMERO;
	regrasTemporarias: any[] = [];
	selectedRegraIndex: number | null = null;
	expandedConfiguracoes: Set<string> = new Set();

	tiposProcessamento = [
		{ valor: TipoProcessamento.PAGAMENTO, nome: 'Pagamento' },
		{ valor: TipoProcessamento.RESSARCIMENTO, nome: 'Ressarcimento' },
	];

	tiposConfiguracao = [
		{ valor: TipoConfiguracao.DIAS_APOS_ENTREGA, nome: 'Dias após prazo de entrega' },
		{ valor: TipoConfiguracao.STATUS_VEICULO, nome: 'Situação do veículo' },
		{ valor: TipoConfiguracao.STATUS_NF_VENDA, nome: 'Situação da nota fiscal de venda' },
		{ valor: TipoConfiguracao.STATUS_NF_DEVOLUCAO, nome: 'Situação da nota fiscal de devolução' },
		{ valor: TipoConfiguracao.DIAS_APOS_EXCLUSAO, nome: 'Quantidade de dias após a exclusão' },
	];

	operadores = [
		{ valor: Operador.IGUAL, nome: 'Igual a (==)' },
		{ valor: Operador.DIFERENTE, nome: 'Diferente de (!=)' },
		{ valor: Operador.MAIOR, nome: 'Maior que (>)' },
		{ valor: Operador.MENOR, nome: 'Menor que (<)' },
		{ valor: Operador.CONTEM, nome: 'Contém' },
	];

	statusVeiculosOpcoes = Object.values(StatusVeiculo).map((s) => ({ nome: s, valor: s }));
	Tipagem = Tipagem;

	tipoConfiguracaoMapa: Record<number, { tipagem: Tipagem; operador: Operador }> = {
		[TipoConfiguracao.DIAS_APOS_ENTREGA]: { tipagem: Tipagem.NUMERO, operador: Operador.MAIOR },
		[TipoConfiguracao.STATUS_VEICULO]: { tipagem: Tipagem.ENUM_STATUS_VEICULO, operador: Operador.IGUAL },
		[TipoConfiguracao.STATUS_NF_VENDA]: { tipagem: Tipagem.TEXTO, operador: Operador.IGUAL },
		[TipoConfiguracao.STATUS_NF_DEVOLUCAO]: { tipagem: Tipagem.TEXTO, operador: Operador.IGUAL },
		[TipoConfiguracao.DIAS_APOS_EXCLUSAO]: { tipagem: Tipagem.NUMERO, operador: Operador.MAIOR },
	};

	constructor(
		private fb: FormBuilder,
		private regrasProcessamentoService: RegrasProcessamentoService,
		private authService: AuthService,
		private cdr: ChangeDetectorRef,
		private dialogoService: DialogoService
	) {}

	ngOnInit(): void {
		this.carregarRegras();
		this.regrasForm = this.fb.group({
			tipoProcessamento: ['', Validators.required],
			tipoConfiguracao: ['', Validators.required],
			nomeRegra: ['', Validators.required],
			valor: [''],
			operador: ['', Validators.required],
			tipagem: [''],
			ativo: [true, Validators.required],
		});
	}

	onTipoProcessamentoChange(value: any): void {
		this.regrasForm.get('tipoProcessamento')?.setValue(Number(value));
	}

	onTipoConfiguracaoChange(value: any): void {
		this.regrasForm.get('tipoConfiguracao')?.setValue(Number(value));
		this.atualizarCamposDinamicamente(Number(value));
	}

	onValorChange(value: any): void {
		this.regrasForm.get('valor')?.setValue(value);
	}

	onOperadorChange(value: any): void {
		this.regrasForm.get('operador')?.setValue(Number(value));
	}

	atualizarCamposDinamicamente(tipoConfiguracao: TipoConfiguracao) {
		const map = this.tipoConfiguracaoMapa[tipoConfiguracao];
		if (map) {
			this.regrasForm.patchValue({ operador: map.operador, tipagem: map.tipagem });
			this.tipagemAtual = map.tipagem;
		} else {
			this.regrasForm.patchValue({ operador: '', tipagem: '' });
			this.tipagemAtual = Tipagem.NUMERO;
		}
	}

	carregarRegras() {
		this.regrasProcessamentoService.getRegras().subscribe((c) => {
			this.configuracoes = c;
			this.cdr.detectChanges();
		});
	}

	salvarRegra() {
		if (!this.regrasForm.valid) return;
		if (this.editando) {
			this.editarRegra();
		} else {
			this.salvarConfiguracao();
		}
	}

	adicionarRegraTemporaria() {
		if (!this.regrasForm.valid) return;

		const f = this.regrasForm;

		// Valida apenas os campos da regra
		const valor = f.get('valor')?.value;
		const operador = f.get('operador')?.value;
		const tipagem = f.get('tipagem')?.value;
		const ativo = f.get('ativo')?.value;

		if (valor === null || valor === undefined || valor === '' || operador === '' || tipagem === '') {
			alert('Preencha os campos da regra antes de adicionar.');
			return;
		}

		const regra = {
			Nome: f.get('nomeRegra')?.value,
			Valor: valor,
			Tipagem: tipagem,
			Operador: Number(operador),
			Ativo: !!ativo,
			IdConfiguracao: null,
		};

		if (this.selectedRegraIndex !== null) {
			this.regrasTemporarias[this.selectedRegraIndex] = regra;
			this.selectedRegraIndex = null;
		} else {
			this.regrasTemporarias.push(regra);
		}

		// Limpa apenas os campos específicos da regra para facilitar adicionar outra
		this.regrasForm.patchValue({ valor: '' });
	}

	editarRegraTemporaria(index: number) {
		const r = this.regrasTemporarias[index];
		if (!r) return;
		this.selectedRegraIndex = index;
		this.regrasForm.patchValue({
			valor: r.Valor,
			operador: r.Operador,
			tipagem: r.Tipagem,
		});
	}

	removerRegraTemporaria(index: number) {
		this.regrasTemporarias.splice(index, 1);
	}

	salvarConfiguracao() {
		if (!this.regrasTemporarias.length) {
			alert('Adicione ao menos uma regra antes de salvar a configuração.');
			return;
		}

		const f = this.regrasForm;
		const nowIso = new Date().toISOString();
		const usuarioAtual = this.authService.getUsuarioAtual()?.name || '';

		if (this.editando && this.idConfiguracaoEmEdicao) {
			const regrasAtualizadas = this.regrasTemporarias.map((r: any) => ({
				Id: r.Id,
				Nome: r.Nome,
				Valor: r.Valor,
				Tipagem: r.Tipagem,
				Operador: r.Operador,
				Ativo: r.Ativo,
			}));

			const payloadAtualiza = {
				Id: this.idConfiguracaoEmEdicao,
				Nome: f.get('nomeRegra')?.value,
				TipoProcessamento: Number(f.get('tipoProcessamento')?.value),
				TipoConfiguracao: Number(f.get('tipoConfiguracao')?.value),
				Ativo: f.get('ativo')?.value,
				AtualizadoEm: nowIso,
				AlteradoPor: usuarioAtual,
				Regras: regrasAtualizadas,
			} as any;

			this.regrasProcessamentoService.editarConfiguracao(this.idConfiguracaoEmEdicao, payloadAtualiza).subscribe(() => {
				this.dialogoService
					.dialogoNotificacao({
						titulo: 'Sucesso',
						descricao: 'Configuração atualizada com sucesso!',
						mostrarBotaoCancelar: false,
					}).subscribe();

				this.carregarRegras();
				this.cancelarEdicao();
			});
		} else {
			const regrasCriacao = this.regrasTemporarias.map((r: any) => ({
				Nome: r.Nome,
				Valor: r.Valor,
				Tipagem: r.Tipagem,
				Operador: r.Operador,
				Ativo: r.Ativo,
			}));

			const payloadCria = {
				Nome: f.get('nomeRegra')?.value,
				TipoProcessamento: Number(f.get('tipoProcessamento')?.value),
				TipoConfiguracao: Number(f.get('tipoConfiguracao')?.value),
				Ativo: f.get('ativo')?.value,
				CriadoPor: usuarioAtual,
				CriadoEm: nowIso,
				Regras: regrasCriacao,
			} as any;

			this.regrasProcessamentoService.criarConfiguracao(payloadCria).subscribe(() => {
                this.dialogoService
					.dialogoNotificacao({
						titulo: 'Sucesso',
						descricao: 'Configuração salva com sucesso!',
						mostrarBotaoCancelar: false,
					}).subscribe();

				this.carregarRegras();
				this.regrasForm.reset({
					tipoProcessamento: '',
					tipoConfiguracao: '',
					nomeRegra: '',
					valor: '',
					operador: '',
					tipagem: '',
					ativo: true,
				});
				this.regrasTemporarias = [];
				this.tipagemAtual = Tipagem.NUMERO;
			});
		}
	}

	carregarRegraParaEdicao(config: ConfiguracaoProcessamento) {
		const configId = config.Id || (config as any).id;

		this.editando = true;
		this.idConfiguracaoEmEdicao = configId;

		// Carrega dados gerais da configuração e zera lista temporária
		this.regrasForm.patchValue({
			tipoProcessamento: config.tipoProcessamento,
			tipoConfiguracao: config.tipoConfiguracao,
			nomeRegra: config.nome,
			valor: '',
			operador: '',
			tipagem: '',
			ativo: config.ativo,
		});

		// Copia todas as regras da configuração para a lista temporária de edição
		this.regrasTemporarias = (config.regras || []).map((r) => ({
			Id: (r as any).Id || (r as any).id,
			IdConfiguracao: configId,
			Nome: config.nome,
			Valor: r.valor,
			Tipagem: r.tipagem,
			Operador: r.operador,
			Ativo: r.ativo,
		}));

		// Ajusta tipagemAtual conforme o tipoConfiguracao selecionado
		this.atualizarCamposDinamicamente(config.tipoConfiguracao as any);
		this.selectedRegraIndex = null;
	}

	async editarRegra() {
        const confirm = await this.dialogoService
					.dialogoNotificacao({
						titulo: 'Confirmar Edição',
						descricao: 'Você tem certeza que deseja salvar as alterações?',
					}).subscribe();

		if (!confirm || !this.idConfiguracaoEmEdicao) return;

		// Reaproveita salvarConfiguracao que envia o array completo de regras atualizado
		this.salvarConfiguracao();
	}

	excluirRegraClick(config: any, regra: any) {
		const idConfiguracao = config.Id || config.id;
		const idRegra = regra.Id || regra.id;

		if (!idConfiguracao || !idRegra) {
			alert('Erro: IDs da configuração ou regra não encontrados.');
			return;
		}

		this.excluirRegra(idConfiguracao, idRegra);
	}

	async excluirRegra(idConfiguracao: string, idRegra: string) {
        const confirm = await this.dialogoService
					.dialogoNotificacao({
						titulo: 'Confirmar Exclusão',
						descricao: 'Você tem certeza que deseja excluir esta regra?',
					}).subscribe();

		if (confirm) {
			this.regrasProcessamentoService.excluirRegra(idConfiguracao, idRegra).subscribe({
				next: () => {
                    this.dialogoService
                        .dialogoNotificacao({
                            titulo: 'Sucesso',
                            descricao: 'Regra excluída com sucesso!',
                            mostrarBotaoCancelar: false,
                        }).subscribe();

					// Se estou editando esta configuração, remove também da lista temporária
					if (this.editando && this.idConfiguracaoEmEdicao === idConfiguracao) {
						this.regrasTemporarias = this.regrasTemporarias.filter((r) => (r as any).Id !== idRegra);
					}
					this.carregarRegras();
				},
				error: (error) => {
					console.error('DELETE request failed:', error);
					console.error('Error details:', {
						status: error.status,
						statusText: error.statusText,
						url: error.url,
						message: error.message,
						error: error.error,
					});
					alert(`Erro ao excluir regra: ${error.status} - ${error.statusText || error.message}`);
				},
			});
		}
	}

	async excluirConfiguracao(config: any) {
		const idConfiguracao = config.Id || config.id;

		if (!idConfiguracao) {
			console.error('ID da configuração não encontrado.', { config });
			alert('Erro: ID da configuração não encontrado.');
			return;
		}

        const confirm = await this.dialogoService
                        .dialogoNotificacao({
                            titulo: 'Confirmar Exclusão',
                            descricao: 'Você tem certeza que deseja excluir a configuração "${config.nome}" e todas as suas regras? Esta ação não pode ser desfeita.',
                        }).subscribe();
        
		if (confirm) {
			this.regrasProcessamentoService.excluirConfiguracao(idConfiguracao).subscribe({
				next: () => {
                    this.dialogoService
                        .dialogoNotificacao({
                            titulo: 'Sucesso',
                            descricao: 'Configuração excluída com sucesso!',
                            mostrarBotaoCancelar: false,
                        }).subscribe();

					// Se estou editando esta configuração, cancela a edição
					if (this.editando && this.idConfiguracaoEmEdicao === idConfiguracao) {
						this.cancelarEdicao();
					}

					this.carregarRegras();
				},
				error: (error) => {
					console.error('DELETE configuration request failed:', error);
					console.error('Error details:', {
						status: error.status,
						statusText: error.statusText,
						url: error.url,
						message: error.message,
						error: error.error,
					});
					alert(`Erro ao excluir configuração: ${error.status} - ${error.statusText || error.message}`);
				},
			});
		}
	}

	cancelarEdicao() {
		this.editando = false;
		this.idConfiguracaoEmEdicao = null;
		this.regrasForm.reset();
		this.regrasTemporarias = [];
		this.tipagemAtual = Tipagem.NUMERO;
	}

	getOperadorLabel(op: Operador | number): string {
		return this.operadores.find((o) => o.valor === op)?.nome || String(op);
	}

	toggleConfiguracao(config: any): void {
		const configId = config.Id || config.id;
		if (!configId) return;

		if (this.expandedConfiguracoes.has(configId)) {
			this.expandedConfiguracoes.delete(configId);
		} else {
			this.expandedConfiguracoes.add(configId);
		}
	}

	isConfiguracaoExpanded(config: any): boolean {
		const configId = config.Id || config.id;
		return configId ? this.expandedConfiguracoes.has(configId) : false;
	}

	getExpandCollapseIcon(config: any): string {
		return this.isConfiguracaoExpanded(config) ? 'feather icon-chevron-down' : 'feather icon-chevron-right';
	}

	expandAllConfiguracoes(): void {
		this.configuracoes.forEach((config) => {
			const configId = config.Id || (config as any).id;
			if (configId) {
				this.expandedConfiguracoes.add(configId);
			}
		});
	}

	collapseAllConfiguracoes(): void {
		this.expandedConfiguracoes.clear();
	}

	hasAnyExpanded(): boolean {
		return this.expandedConfiguracoes.size > 0;
	}
}
