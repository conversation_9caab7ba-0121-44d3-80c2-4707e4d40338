// Bootstrap custom - importar apenas o necessário
@import '../node_modules/bootstrap/scss/functions';
@import '../node_modules/bootstrap/scss/variables';
@import '../node_modules/bootstrap/scss/mixins';

// Layout & components
@import '../node_modules/bootstrap/scss/root';
@import '../node_modules/bootstrap/scss/reboot';
@import '../node_modules/bootstrap/scss/type';
@import '../node_modules/bootstrap/scss/images';
@import '../node_modules/bootstrap/scss/containers';
@import '../node_modules/bootstrap/scss/grid';

// Components essenciais
@import '../node_modules/bootstrap/scss/buttons';
@import '../node_modules/bootstrap/scss/forms';
@import '../node_modules/bootstrap/scss/input-group';
@import '../node_modules/bootstrap/scss/dropdown';
@import '../node_modules/bootstrap/scss/nav';
@import '../node_modules/bootstrap/scss/navbar';
@import '../node_modules/bootstrap/scss/card';
@import '../node_modules/bootstrap/scss/pagination';
@import '../node_modules/bootstrap/scss/tables';
@import '../node_modules/bootstrap/scss/badge';

// Utilities essenciais
@import '../node_modules/bootstrap/scss/utilities/api';
@import '../node_modules/bootstrap/scss/utilities';

// Helpers
@import '../node_modules/bootstrap/scss/helpers';
