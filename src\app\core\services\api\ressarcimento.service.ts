import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../auth/services/auth.service';
import { Ressarcimento } from '../../../shared/interfaces/api/ressarcimento.interface';


@Injectable({
  providedIn: 'root'
})

export class RessarcimentoService{

    private readonly API_BASE = !environment.useProxy ? '/api-cors/ressarcimentos' : `${environment.apiUrl}/ressarcimentos`;
    private readonly usaMock: boolean = environment.useMock;

    private http = inject(HttpClient);
    private authService = inject(AuthService);

    constructor() {}

    private buildHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    let headers = new HttpHeaders({});

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }
        return headers;

    }

    listarRessarcimentosCobrados(numeroSinistro: string = '', fornecedor: string = '', dataAutorizacaoInicial: string = '', dataAutorizacaoFinal: string = '', numeroDocumento: string = '', statusReparo: string = '', placa: string = '', divergencias: boolean = false, condicoes: boolean = false): Observable<Ressarcimento[]> {
        const headers = this.buildHeaders();
        let cobradosUrl = `${this.API_BASE}/cobrados?`;
        cobradosUrl += `numeroAgregador=${numeroSinistro}&`;
        cobradosUrl += `fornecedor=${fornecedor}&`;
        cobradosUrl += `dataAutorizacaoInicial=${dataAutorizacaoInicial}&`;
        cobradosUrl += `dataAutorizacaoFinal=${dataAutorizacaoFinal}&`;
        cobradosUrl += `numeroDocumento=${numeroDocumento}&`;
        cobradosUrl += `statusVeiculo=${statusReparo}&`;
        cobradosUrl += `placa=${placa}&`;
        cobradosUrl += `divergencia=${divergencias}&`;
        cobradosUrl += `condicao=${condicoes}`;
        return this.http.get<Ressarcimento[]>(cobradosUrl, { headers });
    }

    listarConcluidosAsync(numeroSinistro: string = '', fornecedor: string = '', dataAutorizacaoInicial: string = '', dataAutorizacaoFinal: string = '', numeroDocumento: string = '', statusReparo: string = '', placa: string = '', divergencias: boolean = false, condicoes: boolean = false): Observable<Ressarcimento[]> {
        const headers = this.buildHeaders();
        let cobradosUrl = `${this.API_BASE}/concluidos?`;
        cobradosUrl += `numeroAgregador=${numeroSinistro}&`;
        cobradosUrl += `fornecedor=${fornecedor}&`;
        cobradosUrl += `dataAutorizacaoInicial=${dataAutorizacaoInicial}&`;
        cobradosUrl += `dataAutorizacaoFinal=${dataAutorizacaoFinal}&`;
        cobradosUrl += `numeroDocumento=${numeroDocumento}&`;
        cobradosUrl += `statusVeiculo=${statusReparo}&`;
        cobradosUrl += `placa=${placa}&`;
        cobradosUrl += `divergencia=${divergencias}&`;
        cobradosUrl += `condicao=${condicoes}`;
        return this.http.get<Ressarcimento[]>(cobradosUrl, { headers });
    }

}