import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { PessoaDadoBancario } from '../../../shared/interfaces/api/pessoa-dado-bancario.interface';

@Injectable({
  providedIn: 'root'
})
export class DadoBancarioService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  atualizar(id: number, dadoBancario: PessoaDadoBancario): Observable<PessoaDadoBancario> {
    return this.http.put<PessoaDadoBancario>(`${this.API_BASE}/dadoBancario/${id}`, dadoBancario);
  }

  deletar(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/dadoBancario/${id}`);
  }
}