// Estilos para o Modal de Notificação
.modal-header {
  border-bottom: none;
  padding: 1.5rem;
  border-radius: 0px !important;
  
  &.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
  }
  
  &.bg-danger {
    background: linear-gradient(135deg, #dc3545, #fd7e14) !important;
  }
  
  &.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
  }
  
  &.bg-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
  }

  .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
  }

  i {
    font-size: 1.5rem;
  }
}

.modal-body {
  padding: 2rem 1.5rem;
  
  .conteudo-notificacao {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    
    .icone-notificacao {
      flex-shrink: 0;
      
      i {
        font-size: 3rem;
        opacity: 0.8;
      }
    }
    
    .mensagem-notificacao {
      flex: 1;
      
      p {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #495057;
      }
    }
  }
}

.modal-footer {
  justify-content: space-between;
  padding: 1rem;

  &.single-button {
    justify-content: flex-end;
  }

  button {
    i {
      margin-right: 0.5rem;
      font-size: 1rem;
      vertical-align: middle;

      &.spin {
        animation: spin 1s linear infinite;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.65;
    }
  }
}

// Animações
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Cores específicas para ícones de notificação
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #b48700 !important;
}

.text-info {
  color: #17a2b8 !important;
}

// Responsividade
@media (max-width: 576px) {
  .modal-header {
    padding: 1rem;
    
    .modal-title {
      font-size: 1.1rem;
    }
    
    i {
      font-size: 1.25rem;
    }
  }
  
  .modal-body {
    padding: 1.5rem 1rem;
    
    .conteudo-notificacao {
      .icone-notificacao i {
        font-size: 2.5rem;
      }
      
      .mensagem-notificacao p {
        font-size: 1rem;
      }
    }
  }
  
  .modal-footer {
    padding: 1rem;
    
    button {
      min-width: 80px;
      font-size: 0.9rem;
    }
  }
}

// Melhorias de acessibilidade
.modal-header,
.modal-footer button {
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
}

// Customizações para Bootstrap modal
.modal-dialog {
  max-width: 500px;
}

// Estado de carregamento
.btn.loading {
  pointer-events: none;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}


.modal-header.header-info {
  background-color: #f1c240;
  h4{
    color: #2c313a;
  }
}

.modal-header.header-padrao {
  background-color: #3f4d67;
  h4{
    color: #ffffff;
  }
}

.modal-header.header-erro {
  background-color: #dc3545;
  h4{
    color: #ffffff;
  }
}