<app-card cardTitle="Ressarcimentos" blockClass="p-0">
	<ul ngbNav #nav="ngbNav" class="nav-tabs">
		<!--<li ngbNavItem="1" class="pb-3">
			<a ngbNavLink>A Cobrar</a>
			<ng-template ngbNavContent>
				 <PERSON><PERSON><PERSON><PERSON> da aba "A Cobrar"
			</ng-template>
		</li>-->
		<li ngbNavItem="2" class="pb-3">
			<a ngbNavLink>Cobrados</a>
			<ng-template ngbNavContent>
				<ng-container>
					<div>
						<app-ressarcimentos-cobrados></app-ressarcimentos-cobrados>
						
					</div>
				</ng-container>
			</ng-template>
		</li>
		<li ngbNavItem="3" class="pb-3">
			<a ngbNavLink>Concluídos</a>
			<ng-template ngbNavContent>
				<ng-container>
					<div>
						<app-ressarcimentos-concluidos></app-ressarcimentos-concluidos>

					</div>
				</ng-container>
			</ng-template>
		</li>
	</ul>

	<div [ngbNavOutlet]="nav">Teste</div>
</app-card>
