import { CommonModule } from '@angular/common';
import { CurrencyBrDirective } from '../../../shared/directives/currency-br.directive';
import { Component, OnInit, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MockDataService } from '../../../shared/services/mock-data.service';

@Component({
	selector: 'app-pagamento-ressarcimento',
	imports: [CommonModule, ReactiveFormsModule, CurrencyBrDirective],
	templateUrl: './pagamento-ressarcimento.component.html',
	styleUrls: ['./pagamento-ressarcimento.component.scss'],
})
export class PagamentoRessarcimentoComponent implements OnInit {
	comprovanteFile: File | null = null;

	formulario: FormGroup;
	isLoading = signal(false);
	errorMessage = '';
	valorParam: string = '';
	idAgregador: string = '';

	private fb = inject(FormBuilder);
	private dialogoService = inject(DialogoService);
	private route = inject(ActivatedRoute);
	private mockDataService = inject(MockDataService);
	private router = inject(Router);

	constructor() {
		this.formulario = this.fb.group({
			tipoPagamento: ['realizado', Validators.required],
			dataPagamento: [null],
			dataAgendada: [null],
			valor: [null, [Validators.required]],
		});

		this.valorParam = this.route.snapshot.queryParamMap.get('valor')?.replace('.', ',') || '';
		this.idAgregador = this.route.snapshot.queryParamMap.get('idAgregador') || '';
	}

	ngOnInit(): void {
		this.formulario.get('tipoPagamento')?.valueChanges.subscribe(() => {
			this.formulario.patchValue({
				dataPagamento: null,
				dataAgendada: null,
			});
		});
	}

	onComprovanteChange(event: Event): void {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			this.comprovanteFile = input.files[0];
		} else {
			this.comprovanteFile = null;
		}
	}
	arrumaValor(valor: string): string {
		let valorTransformado = '';
		if (valor.length > 0) {
			// Procura a possicao da virgula
			const posicaoVirgula = valor.indexOf(',');
			let valorSemVirgula = valor.replace(/,/g, '');
			if (posicaoVirgula !== -1) {
				valorTransformado = valorSemVirgula.slice(0, posicaoVirgula + 1) + '.' + valorSemVirgula.slice(posicaoVirgula + 1);
			} else {
				valorTransformado = '0.0' + valorSemVirgula;
			}
		}

		return parseFloat(valorTransformado).toFixed(2);
	}

	private validarDatas(): boolean {
		const tipoPagamento = this.formulario.get('tipoPagamento')?.value;
		const dataPagamento = this.formulario.get('dataPagamento')?.value;
		const dataAgendada = this.formulario.get('dataAgendada')?.value;
		if (tipoPagamento === 'realizado') {
			if (!dataPagamento) {
				this.errorMessage = 'Informe a data do pagamento.';
				return false;
			}
		} else if (tipoPagamento === 'agendar') {
			if (!dataAgendada) {
				this.errorMessage = 'Informe a data agendada para pagamento.';
				return false;
			}
		}
		// Validação do valor
		const valorStr = this.formulario.get('valor')?.value || '';

		let valorTransformado = this.arrumaValor(valorStr);
		const valorNum = parseFloat(valorTransformado);

		if (isNaN(valorNum) || valorNum <= 0) {
			this.errorMessage = 'Informe um valor maior que zero.';
			return false;
		}
		return true;
	}

	private formatCurrency(value: string): string {
		const [int, dec] = value.split('.');
		const intFormatted = int.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
		return `${intFormatted},${dec}`;
	}

	enviar(): void {
		this.isLoading.set(true);

		if (this.formulario.valid && this.validarDatas()) {
			const valorForm = this.formulario.get('valor')?.value || '';
			let valorCorrigido = this.arrumaValor(valorForm);
			const tipoPagamento = this.formulario.get('tipoPagamento')?.value;
			const valor = valorCorrigido;
			const dataPagamento = this.formulario.get('dataPagamento')?.value;
			const dataAgendada = this.formulario.get('dataAgendada')?.value;

			if (tipoPagamento === 'realizado') {
				if (!this.comprovanteFile) {
					this.isLoading.set(false);
					this.errorMessage = 'Envie o comprovante de pagamento.';
					return;
				}
			}

			const formatarDataBr = (data: string) => {
				if (!data) return '';
				if (/^\d{4}-\d{2}-\d{2}$/.test(data)) {
					const [ano, mes, dia] = data.split('-');
					return `${dia}/${mes}/${ano}`;
				}
				const d = new Date(data);
				if (isNaN(d.getTime())) return data;
				const dia = String(d.getDate()).padStart(2, '0');
				const mes = String(d.getMonth() + 1).padStart(2, '0');
				const ano = d.getFullYear();
				return `${dia}/${mes}/${ano}`;
			};
			const dataPagamentoBr = formatarDataBr(dataPagamento);
			const dataAgendadaBr = formatarDataBr(dataAgendada);

			// Atualiza os valores de ressarcimento no mock
			const valorNumerico = parseFloat(valorCorrigido);
			this.mockDataService.atualizarRessarcimentoService(this.idAgregador, valorNumerico).subscribe({
				next: () => {
					this.isLoading.set(false);
					this.dialogoService
						.dialogoNotificacao({
							titulo: tipoPagamento === 'realizado' ? 'Pagamento realizado!' : 'Pagamento agendado!',
							descricao:
								tipoPagamento === 'realizado'
									? `Pagamento de R$ ${this.formatCurrency(valorCorrigido)} realizado em ${dataPagamentoBr}.`
									: `Pagamento de R$ ${this.formatCurrency(valorCorrigido)} agendado para ${dataAgendadaBr}.`,
							mostrarBotaoCancelar: false,
							botaoConfirmar: {
								texto: 'OK',
								classe: 'btn-primary',
								icone: 'fa fa-check',
								habilitaIcone: true,
							},
						})
						.subscribe({
							next: () => {
								this.router.navigate(['/movimentacoes/detalhes'], { queryParams: { idMovimentacao: this.idAgregador } });
							},
						});
				},
			});
		} else {
			this.isLoading.set(false);
			this.errorMessage = 'Preencha todos os campos obrigatórios.';
		}
	}
}
