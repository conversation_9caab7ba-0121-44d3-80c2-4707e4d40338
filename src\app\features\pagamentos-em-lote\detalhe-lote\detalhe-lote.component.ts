import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColDef } from 'ag-grid-community';

import { CardComponent } from '../../../shared/components/card/card.component';
import { TabelaPadraoComponent } from '../../../shared/components/tabelas/tabela-padrao/tabela-padrao.component';
import { SelectSimplesComponent } from '../../../shared/components/selects/select-simples/select-simples.component';


import { DetalheLoteService } from '../services/detalhe-lote.service';
import { DetalheLote, LinhaProcessada, FiltrosDetalheLote } from '../interfaces/detalhe-lote.interface';
import { StatusLinhaPagamento, statusLinhaOpcoes } from '../../../shared/enums/status-linha-pagamento.enum';
import { OperacaoLinhaPagamento, operacaoLinhaOpcoes } from '../../../shared/enums/operacao-linha-pagamento.enum';
import { StatusLoteImportacao } from '../../../shared/enums/status-lote-importacao.enum';
import { OpcaoSelect } from '../../../shared/components/selects/select.enum';

@Component({
  selector: 'app-detalhe-lote',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatSnackBarModule,
    CardComponent,
    TabelaPadraoComponent,
    SelectSimplesComponent
  ],
  templateUrl: './detalhe-lote.component.html',
  styleUrl: './detalhe-lote.component.scss'
})
export class DetalheLoteComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private fb = inject(FormBuilder);
  private snackBar = inject(MatSnackBar);
  private modal = inject(NgbModal);
  private detalheLoteService = inject(DetalheLoteService);

  loteId = signal<number>(0);
  detalheLote = signal<DetalheLote | null>(null);
  linhasProcessadas = signal<LinhaProcessada[]>([]);
  carregando = signal(false);
  carregandoLinhas = signal(false);
  carregandoDownload = signal(false);

  // Pagination
  currentPage = signal(1);
  pageSize = signal(20);
  totalItems = signal(0);
  totalPages = signal(0);

  // Form for filters
  filtros = this.fb.group({
    statusLinha: this.fb.control<StatusLinhaPagamento | ''>(''),
    operacaoAplicada: this.fb.control<OperacaoLinhaPagamento | ''>(''),
    numeroNF: this.fb.control<string>(''),
    cnpjs: this.fb.control<string>('')
  });

  // Options for selects
  opcoesStatusLinha: OpcaoSelect[] = statusLinhaOpcoes();
  opcoesOperacao: OpcaoSelect[] = operacaoLinhaOpcoes();

  // Expose Math for template
  Math = Math;

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    {
      headerName: '#Linha',
      field: 'numeroLinha',
      width: 80,
      cellStyle: { textAlign: 'center' }
    },
    {
      headerName: 'Chaves',
      field: 'chaves',
      flex: 2,
      valueGetter: (params) => {
        const data = params.data;
        return `${data.cnpjPagador} | ${data.cnpjCpfFavorecido} | ${data.numeroNF}`;
      },
      cellStyle: { fontSize: '12px' }
    },
    {
      headerName: 'Datas',
      field: 'datas',
      flex: 1.5,
      valueGetter: (params) => {
        const data = params.data;
        const prog = data.dataProgramacao ? new Date(data.dataProgramacao).toLocaleDateString('pt-BR') : '-';
        const liq = data.dataLiquidacao ? new Date(data.dataLiquidacao).toLocaleDateString('pt-BR') : '-';
        return `Prog: ${prog} | Liq: ${liq}`;
      },
      cellStyle: { fontSize: '12px' }
    },
    {
      headerName: 'Valor Pago',
      field: 'valorPago',
      width: 120,
      valueFormatter: (params) => {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(params.value);
      },
      cellStyle: { textAlign: 'right' }
    },
    {
      headerName: 'Operação',
      field: 'operacaoAplicada',
      width: 120,
      valueGetter: (params) => this.getOperacaoLabel(params.data.operacaoAplicada),
      cellRenderer: (params: any) => {
        const operacao = params.data.operacaoAplicada;
        const label = this.getOperacaoLabel(operacao);
        const badgeClass = this.getOperacaoBadgeClass(operacao);
        return `<span class="badge ${badgeClass}">${label}</span>`;
      }
    },
    {
      headerName: 'Busca',
      field: 'busca',
      width: 100,
      valueGetter: (params) => {
        const data = params.data;
        if (data.pagamentoCriado) return 'Criado';
        if (data.pagamentoEncontrado) return 'Encontrado';
        return '-';
      },
      cellStyle: { textAlign: 'center', fontSize: '12px' }
    },
    {
      headerName: 'Resultado',
      field: 'status',
      width: 100,
      cellRenderer: (params: any) => {
        const status = params.data.status;
        const label = status === StatusLinhaPagamento.OK ? 'OK' : 'ERRO';
        const badgeClass = status === StatusLinhaPagamento.OK ? 'bg-success' : 'bg-danger';
        return `<span class="badge ${badgeClass}">${label}</span>`;
      }
    },
    {
      headerName: 'Mensagem',
      field: 'mensagem',
      flex: 2,
      cellStyle: { fontSize: '12px' }
    },
    {
      headerName: 'PagamentoId',
      field: 'idPagamento',
      width: 100,
      valueFormatter: (params) => params.value || '-',
      cellStyle: { textAlign: 'center' }
    },
    {
      headerName: 'Ações',
      field: 'acoes',
      width: 120,
      cellRenderer: (params: any) => {
        const data = params.data;
        if (data.status === StatusLinhaPagamento.Erro || data.operacaoAplicada === OperacaoLinhaPagamento.Ignorado) {
          return `<button class="btn btn-sm btn-outline-primary" onclick="window.reprocessarLinha(${data.id})">Reprocessar</button>`;
        }
        return '';
      },
      cellStyle: { textAlign: 'center' }
    }
  ];

  ngOnInit(): void {
    // Get loteId from route
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loteId.set(parseInt(id, 10));
      this.carregarDetalheLote();
      this.carregarLinhasProcessadas();
    } else {
      this.router.navigate(['/pagamentos/lotes']);
    }

    // Setup global function for reprocess button
    (window as any).reprocessarLinha = (linhaId: number) => {
      this.reprocessarLinha(linhaId);
    };
  }

  carregarDetalheLote(): void {
    this.carregando.set(true);
    this.detalheLoteService.obterDetalheLote(this.loteId()).subscribe({
      next: (detalhe) => {
        this.detalheLote.set(detalhe);
        this.carregando.set(false);
      },
      error: (error) => {
        console.error('Erro ao carregar detalhe do lote:', error);
        this.snackBar.open('Erro ao carregar detalhes do lote', '', { 
          duration: 4000, 
          panelClass: ['snackbar-error'] 
        });
        this.carregando.set(false);
      }
    });
  }

  carregarLinhasProcessadas(): void {
    this.carregandoLinhas.set(true);
    const filtros: FiltrosDetalheLote = {
      statusLinha: this.filtros.value.statusLinha || '',
      operacaoAplicada: this.filtros.value.operacaoAplicada || '',
      numeroNF: this.filtros.value.numeroNF || '',
      cnpjs: this.filtros.value.cnpjs || ''
    };

    this.detalheLoteService.obterLinhasProcessadas(
      this.loteId(), 
      filtros, 
      this.currentPage(), 
      this.pageSize()
    ).subscribe({
      next: (response) => {
        this.linhasProcessadas.set(response.linhas);
        this.totalItems.set(response.totalItems);
        this.totalPages.set(response.totalPages);
        this.carregandoLinhas.set(false);
      },
      error: (error) => {
        console.error('Erro ao carregar linhas processadas:', error);
        this.snackBar.open('Erro ao carregar linhas processadas', '', { 
          duration: 4000, 
          panelClass: ['snackbar-error'] 
        });
        this.carregandoLinhas.set(false);
      }
    });
  }

  buscar(): void {
    this.currentPage.set(1);
    this.carregarLinhasProcessadas();
  }

  limpar(): void {
    this.filtros.reset({
      statusLinha: '',
      operacaoAplicada: '',
      numeroNF: '',
      cnpjs: ''
    });
    this.buscar();
  }

  onStatusLinhaChange(value: string | number): void {
    const statusValue = value === '' ? '' : value as StatusLinhaPagamento;
    this.filtros.controls.statusLinha.setValue(statusValue);
  }

  onOperacaoChange(value: string | number): void {
    const operacaoValue = value === '' ? '' : value as OperacaoLinhaPagamento;
    this.filtros.controls.operacaoAplicada.setValue(operacaoValue);
  }

  baixarArquivo(): void {
    this.carregandoDownload.set(true);
    this.detalheLoteService.baixarArquivoProcessado(this.loteId()).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `lote_${this.loteId()}_processado.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
        this.carregandoDownload.set(false);
        this.snackBar.open('Download iniciado com sucesso', '', { 
          duration: 3000, 
          panelClass: ['snackbar-success'] 
        });
      },
      error: (error) => {
        console.error('Erro ao baixar arquivo:', error);
        this.snackBar.open('Erro ao baixar arquivo', '', { 
          duration: 4000, 
          panelClass: ['snackbar-error'] 
        });
        this.carregandoDownload.set(false);
      }
    });
  }

  reprocessarLinha(linhaId: number): void {
    this.detalheLoteService.reprocessarLinha(this.loteId(), linhaId).subscribe({
      next: (sucesso) => {
        if (sucesso) {
          this.snackBar.open('Linha reprocessada com sucesso', '', { 
            duration: 3000, 
            panelClass: ['snackbar-success'] 
          });
          this.carregarLinhasProcessadas();
        }
      },
      error: (error) => {
        console.error('Erro ao reprocessar linha:', error);
        this.snackBar.open('Erro ao reprocessar linha', '', { 
          duration: 4000, 
          panelClass: ['snackbar-error'] 
        });
      }
    });
  }

  voltarParaLista(): void {
    this.router.navigate(['/pagamentos/lotes']);
  }

  getStatusLoteLabel(status: StatusLoteImportacao): string {
    switch (status) {
      case StatusLoteImportacao.Processando:
        return 'Processando';
      case StatusLoteImportacao.Concluido:
        return 'Concluído';
      case StatusLoteImportacao.ConcluidoComErros:
        return 'Concluído com erros';
      case StatusLoteImportacao.Falha:
        return 'Falha';
      default:
        return 'Desconhecido';
    }
  }

  getStatusLoteBadgeClass(status: StatusLoteImportacao): string {
    switch (status) {
      case StatusLoteImportacao.Processando:
        return 'bg-warning';
      case StatusLoteImportacao.Concluido:
        return 'bg-success';
      case StatusLoteImportacao.ConcluidoComErros:
        return 'bg-warning';
      case StatusLoteImportacao.Falha:
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  }

  private getOperacaoLabel(operacao: OperacaoLinhaPagamento): string {
    switch (operacao) {
      case OperacaoLinhaPagamento.Programar:
        return 'PROGRAMAR';
      case OperacaoLinhaPagamento.Liquidar:
        return 'LIQUIDAR';
      case OperacaoLinhaPagamento.Ignorado:
        return 'IGNORADO';
      case OperacaoLinhaPagamento.Duplicado:
        return 'DUPLICADO';
      case OperacaoLinhaPagamento.Erro:
        return 'ERRO';
      default:
        return 'DESCONHECIDO';
    }
  }

  private getOperacaoBadgeClass(operacao: OperacaoLinhaPagamento): string {
    switch (operacao) {
      case OperacaoLinhaPagamento.Programar:
        return 'bg-primary';
      case OperacaoLinhaPagamento.Liquidar:
        return 'bg-success';
      case OperacaoLinhaPagamento.Ignorado:
        return 'bg-secondary';
      case OperacaoLinhaPagamento.Duplicado:
        return 'bg-warning';
      case OperacaoLinhaPagamento.Erro:
        return 'bg-danger';
      default:
        return 'bg-light';
    }
  }
}
