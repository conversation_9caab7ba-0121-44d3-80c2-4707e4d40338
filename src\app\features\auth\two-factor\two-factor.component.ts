import { Component, OnInit, OnDestroy, inject, signal, effect } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../core/auth/services/auth.service';
import { ConfirmacaoEmailRequest, ConfirmacaoEmailResponse, verificaCodigoEmailRequest } from '../../../shared/interfaces/auth';

@Component({
	selector: 'app-two-factor',
	standalone: true,
	imports: [CommonModule, ReactiveFormsModule],
	templateUrl: './two-factor.component.html',
	styleUrls: ['./two-factor.component.scss'],
})
export class TwoFactorComponent implements OnInit, OnDestroy {
	private fb = inject(FormBuilder);
	private authService = inject(AuthService);
	private router = inject(Router);
	private route = inject(ActivatedRoute);

	doisFatForm: FormGroup;
	carregando = signal(false);
	username = signal('');
	codigoReenviado = signal(false);
	reenvioContagem = signal(60);
	reenvioTimer: any;

    erroEnvioEmail = signal(false);
    mensagemEnvioEmail = signal('Digite o código de 6 dígitos enviado para o e-mail');
    erroVerificaCodigo = signal(false);
    mensagemVerificaCodigo = signal('');

	constructor() {
		this.doisFatForm = this.fb.group({
			codigo: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
		});
	}

	ngOnInit(): void {
		this.route.queryParams.subscribe((params) => {
			this.username.set(params['username'] || '');
			if (!this.username()) {
				this.router.navigate(['/login']);
				return;
			}

			// this.enviaCodigoEmail();
		});
	}

	ngOnDestroy(): void {
		if (this.reenvioTimer) {
			clearInterval(this.reenvioTimer);
		}
	}

	enviaCodigoEmail(): void {
		if (!this.username()) return;

		this.carregando.set(true);

		const request: ConfirmacaoEmailRequest = {
			username: this.username(),
		};

		this.authService.confirmacaoEmail(request).subscribe({
			next: (response: ConfirmacaoEmailResponse | null) => {
				this.carregando.set(false);

				if (response) {
					this.mensagemEnvioEmail.set('Digite o código de 6 dígitos enviado para o e-mail');
                    this.erroEnvioEmail.set(false);
					this.iniciaContatorReenvio();
				} else {
                    this.mensagemEnvioEmail.set('Erro ao enviar código para o e-mail');
                    this.erroEnvioEmail.set(true);
				}
			},
			error: () => {
				this.mensagemEnvioEmail.set('Erro ao enviar código para o e-mail');
                this.erroEnvioEmail.set(true);
			},
		});
	}

	onSubmit(): void {
		if (this.doisFatForm.valid && !this.carregando()) {
			this.carregando.set(true);
			this.erroVerificaCodigo.set(false);
            this.mensagemVerificaCodigo.set('');

			const request: verificaCodigoEmailRequest = {
				username: this.username(),
				code: this.doisFatForm.get('codigo')?.value,
			};

			this.authService.verificaCodigoEmail(request).subscribe({
				next: (response) => {
					this.carregando.set(false);
					if (response.success) {
                        this.erroVerificaCodigo.set(false);
                        this.mensagemVerificaCodigo.set('Autenticação realizada com sucesso!');

						setTimeout(() => {
							this.router.navigate(['/pagina-inicial']);
						}, 1000);

					} else {
                        this.erroVerificaCodigo.set(true);
                        this.mensagemVerificaCodigo.set(response?.result?.message || 'Código inválido');
					}
				},
				error: (error) => {
                    this.carregando.set(false);
                    this.erroVerificaCodigo.set(true);
                    this.mensagemVerificaCodigo.set(error?.result?.message || 'Código inválido. Tente novamente.');
				},
			});
		}
	}

	reenviarCodigoConfirm(): void {
		if (!this.codigoReenviado()) {
			this.enviaCodigoEmail();
		}
	}

	private iniciaContatorReenvio(): void {
		this.codigoReenviado.set(true);
		this.reenvioContagem.set(60);

		this.reenvioTimer = setInterval(() => {
			const currentCooldown = this.reenvioContagem();
			this.reenvioContagem.set(currentCooldown - 1);
			if (this.reenvioContagem() <= 0) {
				this.codigoReenviado.set(false);
				clearInterval(this.reenvioTimer);
			}
		}, 1000);
	}

	voltar(): void {
		this.router.navigate(['/login']);
	}
}
