.modal-header {
  background-color: #f8f9fa;
}

.modal-footer {
  justify-content: space-between;
  padding: 1rem;

  &.single-button {
    justify-content: flex-end;
  }

  button {
    i {
      margin-right: 0.5rem;
      font-size: 1rem;
      vertical-align: middle;

      &.spin {
        animation: spin 1s linear infinite;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.65;
    }
  }
}

.btn {
  min-width: 100px;
  margin: 0 0.25rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.modal-header.header-info {
  background-color: #f1c240;
  h4{
    color: #2c313a;
  }
}

.modal-header.header-padrao {
  background-color: #3f4d67;
  h4{
    color: #ffffff;
  }
}

.modal-header.header-erro {
  background-color: #dc3545;
  h4{
    color: #ffffff;
  }
}
