import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../core/auth/services/auth.service';
import { DialogoService } from '../../core/services/dialogos/dialogo.service';
import { SnackbarSuccessComponent } from './snackbar-success.component';
import { ConviteNovoUsuarioRequest } from '../../shared/interfaces/auth/requests';

@Component({
	selector: 'app-enviar-convite',
	standalone: true,
	imports: [ReactiveFormsModule, CommonModule, MatSnackBarModule, SnackbarSuccessComponent],
	templateUrl: './enviar-convite.component.html',
	styleUrls: ['./enviar-convite.component.scss'],
})
export class EnviarConviteComponent {
	conviteForm: FormGroup;
	enviado = false;

  constructor(
	private fb: FormBuilder,
	private authService: AuthService,
	private snackBar: MatSnackBar,
	private dialogoService: DialogoService
  ) {

		this.conviteForm = this.fb.group({
			email: ['', [Validators.required, Validators.email]],
		});
	}

	onSubmit(): void {
		this.enviado = true;

		const clientes = JSON.parse(this.authService.getUsuarioAtual()?.clientes.toString() || '[]')
			.map((cliente: any) => cliente.id);

		const dadosNovoUsuario: ConviteNovoUsuarioRequest = {
			email: this.conviteForm.value.email,
			clientes: clientes,
		};

		if (this.conviteForm.valid) {
			this.authService.enviarConviteNovoUsuario(dadosNovoUsuario).subscribe({
				next: () => {
					this.snackBar.open('Convite enviado com sucesso!', '', {
						duration: 5000,
						panelClass: ['snackbar-success'],
					});
					this.enviado = true;
					this.conviteForm.clearValidators();
					this.conviteForm.updateValueAndValidity();
					this.conviteForm.reset();
				},
		error: (err) => {
		  console.error('Erro ao enviar convite:', err);
		  this.dialogoService.dialogoNotificacao({
			titulo: 'Erro ao enviar convite',
			descricao: err.error.result.message || 'Ocorreu um erro ao enviar o convite.',
			corHeader: 'danger',
			mostrarBotaoCancelar: false,
			botaoConfirmar: {
			  texto: 'OK',
			  classe: 'btn-danger',
			}
		  }).subscribe();
		},
			});
		}
	}
}
