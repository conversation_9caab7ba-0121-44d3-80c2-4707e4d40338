import { StatusLoteImportacao } from '../../../shared/enums/status-lote-importacao.enum';
import { StatusLinhaPagamento } from '../../../shared/enums/status-linha-pagamento.enum';
import { OperacaoLinhaPagamento } from '../../../shared/enums/operacao-linha-pagamento.enum';

export interface DetalheLote {
  id: number;
  nomeArquivo: string;
  dataProcessamento: string;
  usuarioProcessamento: string;
  status: StatusLoteImportacao;
  totalLinhasLidas: number;
  totalProgramados: number;
  totalLiquidados: number;
  totalCriados: number;
  totalIgnorados: number;
  totalDuplicados: number;
  totalErros: number;
  totalProcessados: number;
  totalComProblemas: number;
  temErros: boolean;
  temIgnorados: boolean;
  tipoArquivo: string;
  tamanhoArquivo: number;
  dataInicio: string;
  dataFim: string;
  tempoProcessamento: string;
}

export interface LinhaProcessada {
  id: number;
  numeroLinha: number;
  cnpjPagador: string;
  cnpjCpfFavorecido: string;
  numeroNF: string;
  dataProgramacao: string | null;
  dataLiquidacao: string | null;
  valorPago: number;
  operacaoAplicada: OperacaoLinhaPagamento;
  status: StatusLinhaPagamento;
  mensagem: string;
  idPagamento: number | null;
  pagamentoEncontrado: boolean;
  pagamentoCriado: boolean;
}

export interface LinhasProcessadasResponse {
  linhas: LinhaProcessada[];
  loteInfo: {
    id: number;
    nomeArquivo: string;
    dataProcessamento: string;
    status: StatusLoteImportacao;
    totalLinhasLidas: number;
    totalErros: number;
  };
  totalItems: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
}

export interface FiltrosDetalheLote {
  statusLinha: StatusLinhaPagamento | '';
  operacaoAplicada: OperacaoLinhaPagamento | '';
  numeroNF: string;
  cnpjs: string;
}
