import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as regrasProcessamentoInterface from '../../../shared/interfaces/api/regras-processamento.interface';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RegrasProcessamentoService {

  private readonly API_BASE = environment.useProxy
    ? `${environment.proxyPath}/zinpag/configuracoe`
    : `${environment.apiUrl}/zinpag/configuracoes`;

  constructor(private http: HttpClient) { }

  getRegras(): Observable<regrasProcessamentoInterface.ConfiguracaoProcessamento[]> {
    return this.http.get<regrasProcessamentoInterface.ConfiguracaoProcessamento[]>(`${this.API_BASE}`);
  }

  getConfiguracaoPorId(id: string): Observable<regrasProcessamentoInterface.ConfiguracaoProcessamento> {
    return this.http.get<regrasProcessamentoInterface.ConfiguracaoProcessamento>(`${this.API_BASE}/${id}`);
  }

  criarConfiguracao(configuracao: regrasProcessamentoInterface.ConfiguracaoProcessamento): Observable<regrasProcessamentoInterface.ConfiguracaoProcessamento> {
    return this.http.post<regrasProcessamentoInterface.ConfiguracaoProcessamento>(this.API_BASE, configuracao);
  }

  editarConfiguracao(id: string, configuracao: regrasProcessamentoInterface.ConfiguracaoProcessamento): Observable<regrasProcessamentoInterface.ConfiguracaoProcessamento> {
    return this.http.put<regrasProcessamentoInterface.ConfiguracaoProcessamento>(`${this.API_BASE}/${id}`, configuracao);
  }

  excluirConfiguracao(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${id}`);
  }

  // Regras (rule-level) endpoints
  getRegra(idConfiguracao: string, idRegra: string): Observable<regrasProcessamentoInterface.RegraCondicional> {
    return this.http.get<regrasProcessamentoInterface.RegraCondicional>(`${this.API_BASE}/${idConfiguracao}/regras/${idRegra}`);
  }

  atualizarRegra(idConfiguracao: string, idRegra: string, dto: any): Observable<regrasProcessamentoInterface.RegraCondicional> {
    return this.http.put<regrasProcessamentoInterface.RegraCondicional>(`${this.API_BASE}/${idConfiguracao}/regras/${idRegra}`, dto);
  }

  excluirRegra(idConfiguracao: string, idRegra: string): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${idConfiguracao}/regras/${idRegra}`);
  }
}