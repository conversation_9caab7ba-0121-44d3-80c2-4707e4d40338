<app-card cardTitle="Cadastro de Pessoa Jurídica - {{ pessoa.id ? 'Alteração' : 'Inclusão' }}" blockClass="p-0">
	<ul ngbNav #nav="ngbNav" class="nav-tabs" [activeId]="activeTab">

		<!-- ABA DADOS GERAIS -->
		<li [ngbNavItem]="1" class="pb-3">
			<a ngbNavLink>Dados Gerais</a>
			<ng-template ngbNavContent>
				<form [formGroup]="dadosGeraisForm">

					<!-- RAZÃO SOCIAL -->
					<div class="row ps-4">
						<div class="col-5">
							<label>Razão Social</label>
							<ng-container>
								<div class="mb-4">
									<input #inputRazaoSocial id="razaoSocial" type="text" class="form-control form-control-sm" maxlength="150"
										formControlName="razaoSocial" [(ngModel)]="pessoa.razaoSocial"
										[class.is-invalid]="dadosGeraisForm.get('razaoSocial')?.invalid && dadosGeraisForm.get('razaoSocial')?.touched" />
									@if (dadosGeraisForm.get('razaoSocial')?.hasError('required')) {
									<div class="invalid-feedback">Razão Social é obrigatória</div>
									}
								</div>
							</ng-container>
						</div>
					</div>

					<!-- NOME FANTASIA -->
					<div class="row ps-4">
						<div class="col-5">
							<label>Nome Fantasia</label>
							<ng-container>
								<div class="mb-4">
									<input id="nomeFantasia" type="text" class="form-control form-control-sm" maxlength="100"
										formControlName="nomeFantasia" [(ngModel)]="pessoa.nomeFantasia"
										[class.is-invalid]="dadosGeraisForm.get('nomeFantasia')?.invalid && dadosGeraisForm.get('nomeFantasia')?.touched" />
									@if (dadosGeraisForm.get('nomeFantasia')?.hasError('required')) {
									<div class="invalid-feedback">Nome Fantasia é obrigatória</div>
									}
								</div>
							</ng-container>
						</div>
					</div>

					<!-- CNPJ -->
					<div class="row ps-4">
						<div class="col-3">
							<label>CNPJ</label>
							<ng-container>
								<div class="mb-4">
									<input id="cnpj" type="text" class="form-control form-control-sm" formControlName="cnpj"
										[(ngModel)]="pessoa.cnpj"
										mask="00.000.000/0000-00"
										[class.is-invalid]="dadosGeraisForm.get('cnpj')?.invalid && dadosGeraisForm.get('cnpj')?.touched" />
									@if (dadosGeraisForm.get('cnpj')?.hasError('required')) {
									<div class="invalid-feedback">CNPJ é obrigatório</div>
									}
								</div>
							</ng-container>
						</div>
					</div>

				</form>
			</ng-template>
		</li>

		<!-- ABA ENDEREÇOS -->
		<li [ngbNavItem]="2">
			<a ngbNavLink>Endereços</a>
			<ng-template ngbNavContent>
				<form [formGroup]="enderecosForm">
					<div [ngbCollapse]="edicaoEnderecoFechada">
						<div>

							<!-- CEP -->
							<div class="row ps-4">
								<div class="col-2">
									<label>CEP</label>
									<ng-container>
										<div class="mb-4">
											<input #inputCep id="cep" type="text" class="form-control form-control-sm"
												formControlName="cep" [(ngModel)]="cep"
												mask="00.000-000"
												[class.is-invalid]="enderecosForm.get('cep')?.invalid && enderecosForm.get('cep')?.touched" />
											@if (enderecosForm.get('cep')?.hasError('required')) {
											<div class="invalid-feedback">CEP é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>
							</div>

							<div class="row ps-4">

								<!-- LOGRADOURO -->
								<div class="col-5">
									<label>Logradouro</label>
									<ng-container>
										<div class="mb-4">
											<input #inputLogradouro id="logradouro" type="text" class="form-control form-control-sm"
												formControlName="logradouro" [(ngModel)]="logradouro"
												[class.is-invalid]="enderecosForm.get('logradouro')?.invalid && enderecosForm.get('logradouro')?.touched" />
											@if (enderecosForm.get('logradouro')?.hasError('required')) {
											<div class="invalid-feedback">Logradouro é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>

								<!-- NÚMERO -->
								<div class="col-2">
									<label>Número</label>
									<ng-container>
										<div class="mb-4">
											<input #inputNumero id="numero" type="text" class="form-control form-control-sm" mask="0*" maxlength="9"
												formControlName="numero" [(ngModel)]="numero"
												[class.is-invalid]="enderecosForm.get('numero')?.invalid && enderecosForm.get('numero')?.touched" />
											@if (enderecosForm.get('numero')?.hasError('required')) {
											<div class="invalid-feedback">Número é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>

							</div>
							<div class="row ps-4">

								<!-- COMPLEMENTO -->
								<div class="col-5">
									<label>Complemento</label>
									<ng-container>
										<input #inputComplemento id="complemento" type="text" class="form-control form-control-sm mb-4"
											formControlName="complemento" [(ngModel)]="complemento" />
									</ng-container>
								</div>

								<!-- BAIRRO -->
								<div class="col-5">
									<label>Bairro</label>
									<ng-container>
										<div class="mb-4">
											<input #inputBairro id="bairro" type="text" class="form-control form-control-sm"
												formControlName="bairro" [(ngModel)]="bairro"
												[class.is-invalid]="enderecosForm.get('bairro')?.invalid && enderecosForm.get('bairro')?.touched" />
											@if (enderecosForm.get('bairro')?.hasError('required')) {
											<div class="invalid-feedback">Bairro é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>

							</div>
							<div class="row ps-4">

								<!-- ESTADO -->
								<div class="col-5">
									<label>Estado</label>
									<ng-container>
										<div class="mb-4">
											<app-select-simples id="estado" [opcoes]="UFs" (mudouValor)="onEstadoChange($event)"
												[valorSelecionado]="estado">
											</app-select-simples>
										</div>
									</ng-container>
								</div>

								<!-- CIDADE -->
								<div class="col-5">
									<label>Cidade</label>
									<ng-container>
										<app-select-simples id="cidade" [opcoes]="Cidades" (mudouValor)="onCidadeChange($event)"
											[valorSelecionado]="cidade">
										</app-select-simples>
									</ng-container>
								</div>

							</div>

							<div class="ps-4 mb-n3 pt-4">

								<!-- BOTÃO SALVAR ENDEREÇO -->
								<button id="btnSalvarEndereco" (click)="salvarEndereco()" class="btn btn-primary">Salvar</button>

								<!-- BOTÃO CANCELAR ENDEREÇO -->
								<button id="btnCancelarEndereco" (click)="exibirEdicaoEndereco(false)"
									class="btn btn-outline-primary">Cancelar</button>
							</div>
						</div>
					</div>

					<!-- BOTÃO INCLUIR ENDEREÇO -->
					<div class="ps-4 pt-4 pb-1">
						@if (mostrarBotaoIncluirEndereco) {
						<button id="btnIncluirEndereco" class="btn btn-primary"
							(click)="novoEndereco()">Incluir</button>
						}
					</div>

					<!-- TABELA DE ENDEREÇOS -->
					<div class="ps-4 pe-4 pt-2">
						<app-tabela-padrao id="tabelaEnderecos" [columnDefs]="ColunasGridEnderecos" [pagination]="true"
							[rowData]="pessoa.enderecos ?? []" />
					</div>

				</form>
			</ng-template>
		</li>

		<!-- ABA CONTATOS -->
		<li [ngbNavItem]="3">
			<a ngbNavLink>Contatos</a>
			<ng-template ngbNavContent>
				<form [formGroup]="contatosForm">
					<div [ngbCollapse]="edicaoContatoFechada">
						<div>

							<!-- NOME -->
							<div class="row ps-4">
								<div class="col-5">
									<label>Nome</label>
									<ng-container>
										<div class="mb-4">
											<input #inputContatoNome id="nome" type="text" class="form-control form-control-sm"
												formControlName="nome" [(ngModel)]="contatoNome"
												[class.is-invalid]="contatosForm.get('nome')?.invalid && contatosForm.get('nome')?.touched" />
											@if (contatosForm.get('nome')?.hasError('required')) {
											<div class="invalid-feedback">Nome é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>
							</div>

							<!-- E-MAIL -->
							<div class="row ps-4">
								<div class="col-5">
									<label>E-Mail</label>
									<ng-container>
										<div class="mb-4">
											<input #inputEmail id="email" type="email" class="form-control form-control-sm"
												formControlName="email" [(ngModel)]="contatoEmail"
												[class.is-invalid]="contatosForm.get('email')?.invalid && contatosForm.get('email')?.touched" />
											@if (contatosForm.get('email')?.hasError('required')) {
											<div class="invalid-feedback">E-Mail é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>
							</div>

							<!-- TIPO -->
							<div class="row ps-4 mb-4">
								<div class="col-5">
									<label>Tipo</label>
									<ng-container>
										<div>
											<ng-multiselect-dropdown id="contatoTipo" [placeholder]="'Selecione opções'"
												[data]="contatoTipos" [(ngModel)]="contatoTipos_selecionados"
												[settings]="contatoTipoSettings" formControlName="contatoTipo"
												[class.is-invalid]="contatosForm.get('contatoTipo')?.invalid && contatosForm.get('contatoTipo')?.touched">
											</ng-multiselect-dropdown>
											@if (contatosForm.get('contatoTipo')?.hasError('required') &&
											contatosForm.get('contatoTipo')?.touched) {
											<div class="invalid-feedback">Tipo é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>
							</div>

							<!-- TELEFONES -->
							<div class="row ps-4 mb-3">
								<div class="col-12">
									<label>Telefones</label>
									<ng-container formArrayName="telefones">
										<div *ngFor="let telefoneCtrl of telefones.controls; let i = index"
											[formGroupName]="i">
											<div class="row">

												<!-- ID CONTATO TELEFONE -->
												<input id="id_contato_telefone" type="hidden" formControlName="id_contato_telefone"
													style="width: 100px;" />

												<!-- TIPO -->
												<div class="col-3">
													<div class="mb-4 d-flex align-items-center">
														<label class="me-2">Tipo</label>
														<select id="contatoTipo" class="form-select form-select-sm"
															formControlName="tipo"
															[class.is-invalid]="telefoneCtrl.get('tipo')?.invalid && telefoneCtrl.get('tipo')?.touched">
															@for (tipo of contatoTipoTelefone_Todos; track tipo.valor) {
															<option [value]="tipo.valor">{{ tipo.nome }}</option>
															}
														</select>
													</div>
													@if (telefoneCtrl.get('tipo')?.hasError('required') &&
													telefoneCtrl.get('tipo')?.touched) {
													<div class="invalid-feedback d-block mt-n3">Tipo é obrigatório</div>
													}
												</div>

												<!-- NÚMERO -->
												<div class="col-4">
													<div class="mb-4 d-flex align-items-center">
														<label class="pe-2">Número</label>
														<input id="telefone" type="text" class="form-control form-control-sm"
															formControlName="telefone"
															mask="(00) 0-0000-0000"
															[class.is-invalid]="telefoneCtrl.get('telefone')?.invalid && telefoneCtrl.get('telefone')?.touched" />
													</div>
													@if (telefoneCtrl.get('telefone')?.hasError('required') &&
													telefoneCtrl.get('telefone')?.touched) {
													<div class="invalid-feedback d-block mt-n3">Número é obrigatório
													</div>
													}
												</div>

												<!-- RAMAL -->
												<div class="col-2">
													<div class="mb-4 d-flex align-items-center">
														<label class="pe-2">Ramal</label>
														<input id="ramal" type="text" class="form-control form-control-sm" mask="0*" maxlength="9"
															formControlName="ramal" />
													</div>
												</div>

												<!-- BOTÃO REMOVER TELEFONE -->
												<div class="col-2">
													<div class="mb-4 d-flex align-items-center">
														<button id="btnRemoverTelefone" type="button" class="btn btn-sm btn-danger ms-2"
															(click)="removerTelefone(i)">Remover</button>
													</div>
												</div>
											</div>

										</div>

										<!-- BOTÃO ADICIONAR TELEFONE -->
										<button id="btnAdicionarTelefone" type="button" class="btn btn-sm btn-primary"
											(click)="adicionarTelefone()">Adicionar Telefone</button>

									</ng-container>
								</div>
							</div>

							<!-- ATIVO -->
							<div class="row ps-4 ">
								<div class="col-5">
									<label>Ativo</label>
									<ng-container>
										<div>
											<input #inputAtivo id="ativo" type="checkbox" class="form-check-input"
												formControlName="ativo" [(ngModel)]="contatoAtivo" />
										</div>
									</ng-container>
								</div>
							</div>

							<div class="ps-4 mb-n3 pt-4">

								<!-- BOTÃO SALVAR CONTATO -->
								<button id="btnSalvarContato" (click)="salvarContato()" class="btn btn-primary">Salvar</button>

								<!-- BOTÃO CANCELAR CONTATO -->
								<button id="btnCancelarContato" (click)="cancelarEdicaoContato()"
									class="btn btn-outline-primary">Cancelar</button>
							</div>

						</div>
					</div>

					<!-- BOTÃO INCLUIR CONTATO -->
					<div class="ps-4 pt-4 pb-1">
						@if (mostrarBotaoIncluirContato) {
						<button id="btnIncluirContato" class="btn btn-primary"
							(click)="novoContato()">Incluir</button>
						}
					</div>

					<!-- TABELA DE CONTATOS -->
					<div class="ps-4 pe-4 pt-2">
						<app-tabela-padrao id="tabelaContatos" [columnDefs]="ColunasGridContatos" [pagination]="true"
							[rowData]="pessoa.contatos ?? []" />
					</div>

				</form>
			</ng-template>
		</li>

		<!-- ABA DADOS BANCÁRIOS -->
		<li [ngbNavItem]="4">
			<a ngbNavLink>Dados Bancários</a>
			<ng-template ngbNavContent>
				<form [formGroup]="dadosBancariosForm">
					<div [ngbCollapse]="edicaoDadosBancariosFechada">
						<div>

							<div class="row ps-4">

								<!-- BANCO -->
								<div class="col-5">
									<label>Banco</label>
									<ng-container>
										<div class="mb-4">
											<app-select-simples #inputBanco id="selectBanco" [opcoes]="bancos"
												[valorSelecionado]="banco" (mudouValor)="onBancoChange($event)">
											</app-select-simples>
										</div>
									</ng-container>
								</div>

							</div>

							<div class="row ps-4">

								<!-- AGÊNCIA -->
								<div class="col-2">
									<label>Agência</label>
									<ng-container>
										<div class="mb-4">
											<input #inputAgencia id="agencia" type="text" class="form-control form-control-sm" mask="0*" maxlength="10"
												formControlName="agencia" [(ngModel)]="agencia"
												[class.is-invalid]="dadosBancariosForm.get('agencia')?.invalid && dadosBancariosForm.get('agencia')?.touched" />
											@if (dadosBancariosForm.get('agencia')?.hasError('required')) {
											<div class="invalid-feedback">Agência é obrigatória</div>
											}

										</div>
									</ng-container>
								</div>

								<!-- AGÊNCIA-DV -->
								<div class="col-1">
									<label>Dígito</label>
									<ng-container>
										<div class="mb-4">
											<input #inputAgenciaDV id="agenciaDV" type="text" class="form-control form-control-sm" mask="0*" maxlength="2"
												formControlName="agenciaDV" [(ngModel)]="agenciaDV" />
										</div>
									</ng-container>
								</div>

								<!-- CONTA -->
								<div class="col-3">
									<label>Conta</label>
									<ng-container>
										<div class="mb-4">
											<input #inputConta id="conta" type="text" class="form-control form-control-sm" mask="0*" maxlength="20"
												formControlName="conta" [(ngModel)]="conta"
												[class.is-invalid]="dadosBancariosForm.get('conta')?.invalid && dadosBancariosForm.get('conta')?.touched" />
											@if (dadosBancariosForm.get('conta')?.hasError('required')) {
											<div class="invalid-feedback">Conta é obrigatória</div>
											}
										</div>
									</ng-container>
								</div>

								<!-- CONTA-DV -->
								<div class="col-1">
									<label>Dígito</label>
									<ng-container>
										<div class="mb-4">
											<input #inputContaDV id="contaDV" type="text" class="form-control form-control-sm" mask="0*" maxlength="2"
												formControlName="contaDV" [(ngModel)]="contaDV" 
												[class.is-invalid]="dadosBancariosForm.get('contaDV')?.invalid && dadosBancariosForm.get('contaDV')?.touched"/>
											@if (dadosBancariosForm.get('contaDV')?.hasError('required')) {
											<div class="invalid-feedback">Dígito da Conta é obrigatório</div>
											}												
										</div>
									</ng-container>
								</div>

							</div>

							<div class="row ps-4">

								<!-- TIPO CONTA -->
								<div class="col-5">
									<label>Tipo de Conta</label>
									<ng-container>
										<div class="mb-4">
											<app-select-simples id="selectTipoConta" [opcoes]="tiposConta" [valorSelecionado]="tipoConta" (mudouValor)="onTipoContaChange($event)">
											</app-select-simples>
										</div>
									</ng-container>
								</div>

							</div>

							<div class="row ps-4">

								<!-- TITULAR -->
								<div class="col-5">
									<label>Titular</label>
									<ng-container>
										<div class="mb-4">
											<input #inputTitular id="titular" type="text" class="form-control form-control-sm" maxlength="100"
												formControlName="titular" [(ngModel)]="titular"
												[class.is-invalid]="dadosBancariosForm.get('titular')?.invalid && dadosBancariosForm.get('titular')?.touched" />
											@if (dadosBancariosForm.get('titular')?.hasError('required')) {
											<div class="invalid-feedback">Titular é obrigatória</div>
											}
										</div>
									</ng-container>
								</div>

								<!-- CPF/CNPJ TITULAR -->
								<div class="col-3">
									<label>CPF/CNPJ</label>
									<ng-container>
										<div class="mb-4">
											<input #inputCpfCnpj id="cpfCnpj" type="text" class="form-control form-control-sm"
												formControlName="cpfCnpj" [(ngModel)]="cpfCnpj"
												[class.is-invalid]="dadosBancariosForm.get('cpfCnpj')?.invalid && dadosBancariosForm.get('cpfCnpj')?.touched" />
											@if (dadosBancariosForm.get('cpfCnpj')?.hasError('required')) {
											<div class="invalid-feedback">Cpf/Cnpj é obrigatório</div>
											}
										</div>
									</ng-container>
								</div>

							</div>

							<div class="row ps-4">

								<!-- PIX -->
								<div class="col-5">
									<label>Chave Pix</label>
									<ng-container>
										<div class="mb-4">
											<input #inputPix id="pix" type="text" class="form-control form-control-sm" maxlength="100"
												formControlName="pix" [(ngModel)]="pix" />
										</div>
									</ng-container>
								</div>

							</div>

							<div class="ps-4 mb-n3 pt-4">

								<!-- BOTÃO SALVAR DADO BANCÁRIO -->
								<button id="btnSalvarDadoBancario" (click)="salvarDadoBancario()" class="btn btn-primary">Salvar</button>

								<!-- BOTÃO CANCELAR DADO BANCÁRIO -->
								<button id="btnCancelarDadoBancario" (click)="exibirEdicaoDadoBancario(false)"
									class="btn btn-outline-primary">Cancelar</button>

							</div>
						</div>
					</div>

					<div class="ps-4 pt-4 pb-1">
						
						<!-- BOTÃO INCLUIR DADO BANCÁRIO -->
						@if (mostrarBotaoIncluirDadosBancarios) {
						<button id="btnIncluirDadoBancario" class="btn btn-primary"
							(click)="novoDadoBancario()">Incluir</button>
						}

					</div>

					<div class="ps-4 pe-4 pt-2">

						<!-- TABELA DADOS BANCÁRIOS -->
						<app-tabela-padrao id="tabelaDadosBancarios" [columnDefs]="ColunasGridDadosBancarios" [pagination]="true"
							[rowData]="pessoa.dadosBancarios ?? []" />

					</div>

				</form>
			</ng-template>
		</li>

	</ul>

	<!-- COMPONENTE NECESSÁRIO PARA FUNCIONAR AS ABAS -->
	<div [ngbNavOutlet]="nav"></div>

	<div class="row p-2 pt-3">
		<div class="col-12 d-flex justify-content-end">
			<ng-container>

				<!-- BOTÃO ANTERIOR -->
				<button class="form-control form-control-sm btn btn-primary" style="width: auto;"
					(click)="goToAbaAnterior()" [disabled]="activeTab === 1">Anterior</button>

				<!-- BOTÃO PRÓXIMO -->
				@if (activeTab < 4) {
				<button class="form-control form-control-sm btn btn-primary" style="width: auto;"
					(click)="goToProximaAba()">Próximo</button>
				}

				<!-- BOTÃO SALVAR -->
				@if (activeTab === 4 || (((pessoa?.id ?? 0) > 0) && activeTab == 1)) {
				<button class="btn btn-primary" (click)="salvar()">
					Salvar
				</button>
				}

			</ng-container>
		</div>
	</div>

</app-card>