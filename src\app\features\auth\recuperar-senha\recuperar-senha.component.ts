import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../../core/auth/services/auth.service';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';

@Component({
	selector: 'app-recuperar-senha',
	imports: [
		RouterModule,
		CommonModule,
		ReactiveFormsModule,
		MatCardModule,
		MatFormFieldModule,
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		MatProgressSpinnerModule,
	],
	templateUrl: './recuperar-senha.component.html',
	styleUrl: './recuperar-senha.component.scss',
})
export class RecuperarSenhaComponent {
	private formBuilder = inject(FormBuilder);
	private authService = inject(AuthService);
	private dialogoService = inject(DialogoService);
	private router = inject(Router);

	loginForm: FormGroup;
	isLoading = false;
	hidePassword = true;
	errorMessage = '';

	constructor() {
		this.loginForm = this.formBuilder.group({
			email: ['', [Validators.required, Validators.email]],
		});
	}

	onSubmit(): void {
		if (this.loginForm.valid) {
			this.isLoading = true;
			this.errorMessage = '';

			const email = this.loginForm.value;

			this.authService.recuperarSenha(email).subscribe({
				next: (response: any) => {
					this.isLoading = false;
					this.dialogoService.dialogoNotificacao({
						titulo: 'Recuperação de Senha',
						descricao: 'Instruções para recuperação de senha foram enviadas para o seu e-mail.',
						corHeader: 'success',
						mostrarBotaoCancelar: false,
						botaoConfirmar: {
							texto: 'OK',
						},
					}).subscribe(() => {
						this.router.navigate(['/auth/login']);
					});

				},
				error: (error: any) => {
					this.isLoading = false;
					this.errorMessage = error.error?.message || 'Erro ao fazer login. Verifique suas credenciais.';
				},
			});
		}
	}
}
