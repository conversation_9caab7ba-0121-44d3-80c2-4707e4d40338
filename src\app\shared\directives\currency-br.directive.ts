import { Directive, HostListener, ElementRef } from '@angular/core';

@Directive({
	selector: '[currencyBr]',
})
export class CurrencyBrDirective {
	private el: HTMLInputElement;

	constructor(private elementRef: ElementRef) {
		this.el = this.elementRef.nativeElement;
	}

	@HostListener('input', ['$event'])
	onInput(event: Event): void {
		let value = this.el.value.replace(/\D/g, '');
		if (value.length === 0) {
			this.el.value = '';
			return;
		}
		value = (parseInt(value, 10) / 100).toFixed(2);
		this.el.value = this.formatCurrency(value);
	}

	private formatCurrency(value: string): string {
		const [int, dec] = value.split('.');
		const intFormatted = int.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
		return `${intFormatted},${dec}`;
	}
}
