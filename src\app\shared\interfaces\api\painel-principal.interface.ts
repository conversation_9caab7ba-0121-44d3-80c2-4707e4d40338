interface ValorPorcentagem {
    valor: number;
    porcentagem: number;
}

interface Autorizado {
    total: number;
    exclusaoAntesPagamento: ValorPorcentagem;
    pagoSemExclusao: ValorPorcentagem;
    pagoComExclusao: ValorPorcentagem;
    pagar: ValorPorcentagem;
}

interface Pagar {
    total: number;
    nfsIncluidas: ValorPorcentagem;
    nfsPendentes: ValorPorcentagem;
}

interface Ressarcimento {
    total: number;
    realizados: ValorPorcentagem;
    pendentes: ValorPorcentagem;
}

export interface PainelPrincipal {
    autorizado: Autorizado;
    pagar: Pagar;
    ressarcimento: Ressarcimento;
}
