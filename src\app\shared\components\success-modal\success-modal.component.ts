import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-success-modal',
  templateUrl: './success-modal.component.html',
  styleUrls: ['./success-modal.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class SuccessModalComponent {
  @Input() message = 'Operação realizada com sucesso!';
  @Output() closed = new EventEmitter<void>();

  close() {
    this.closed.emit();
  }
}
