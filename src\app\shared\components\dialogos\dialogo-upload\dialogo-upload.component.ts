import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { FileSizePipe } from '../../../pipes/file-size.pipe';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-dialogo-upload',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    FileSizePipe
  ],
  providers: [ { provide: MatDialogRef, useValue: {} } ],
  templateUrl: './dialogo-upload.component.html',
  styleUrls: ['./dialogo-upload.component.scss']
})
export class DialogoUploadComponent {
  private activeModal = inject(NgbActiveModal);
  
  files = signal<File[]>([]);
  isDragging = signal(false);

  onFileDropped(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(false);
    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(files);
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(true);
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(false);
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.handleFiles(input.files);
    }
  }

  private handleFiles(fileList: FileList) {
    this.files.set(Array.from(fileList));
  }

  upload() {
    if (this.files().length > 0) {
      // Aqui você pode implementar a lógica de upload
      this.activeModal.close(this.files());
    }
  }

  cancel() {
    this.activeModal.close();
  }
}
