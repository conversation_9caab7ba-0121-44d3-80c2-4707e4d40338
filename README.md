# ZIN Frontend Angular

Sistema de autenticação JWT com interface moderna para CRM usando Angular 20.

## 🚀 Funcionalidades

### 🔐 Autenticação JWT
- Login seguro com email/senha
- Endpoints integrados:
  - `POST https://zinidentity.sistemaprismatec.com.br/auth/login`
  - `POST https://zinidentity.sistemaprismatec.com.br/auth/logout`
- Guards de proteção de rotas
- Interceptor HTTP automático
- Persistência de sessão no localStorage


## � Configuração da API

O sistema está configurado para sempre usar a API real diretamente:

### URLs da API:
- **Login**: `https://zinidentity.sistemaprismatec.com.br/auth/login`
- **Logout**: `https://zinidentity.sistemaprismatec.com.br/auth/logout`

### Headers CORS:
O `AuthService` inclui automaticamente os headers necessários para requisições CORS:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer <token>` (quando disponível)

### Tratamento de Erros:
- Erros de CORS (status 0)
- API indisponível (resposta HTML)
- Erros de autenticação (401/403)
- Erros de validação (400)

## �🛠️ Como Executar

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Executar em desenvolvimento:**
   ```bash
   npm start
   ```

3. **Acessar:** http://localhost:4200

## 🔄 Fluxo de Navegação

1. **Início** → Redirecionamento automático para `/login`
2. **Login** → Autenticação → Redirecionamento para `/pagina-inicial`
3. **Dashboard** → Interface principal com sidebar navegável
4. **Clientes** → Página exemplo acessível via `/clientes`
5. **Logout** → Limpeza de sessão → Redirecionamento para `/login`

## 🎯 Características dos Componentes

### Login Component
- Formulário reativo com validação
- Estados de loading
- Tratamento de erros
- Design moderno com gradientes

### Dashboard Component
- Sidebar responsiva que se contrai/expande
- Barra superior com menu do usuário
- Cards de estatísticas
- Área de conteúdo para rotas filhas

### Clients Component
- Página exemplo de funcionalidade CRM
- Cards interativos
- Layout em grid responsivo

## 🔒 Segurança Implementada

- **AuthGuard**: Protege rotas autenticadas
- **LoginGuard**: Evita acesso ao login quando logado
- **HTTP Interceptor**: Adiciona token JWT automaticamente
- **Session Management**: Controle completo de sessão

## 🎨 Design System

- **Material Design**: Angular Material components
- **Responsive Layout**: Mobile-first approach
- **Modern UI**: Gradientes, sombras e animações
- **Consistent Spacing**: Sistema de espaçamento padronizado

## 📦 Dependências Principais

- Angular 20
- Angular Material & CDK
- Angular Animations
- TypeScript
- RxJS
- SCSS

## 🚀 Próximos Passos

- Implementar mais páginas do CRM (Vendas, Produtos, Relatórios)
- Adicionar funcionalidades CRUD
- Implementar temas claro/escuro
- Adicionar testes unitários e e2e
- Configurar CI/CD
