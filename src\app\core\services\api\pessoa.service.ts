import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { PessoasResponse, PessoaResponse, Pessoa } from '../../../shared/interfaces/api/pessoa.interface';
import { TipoPessoa } from '../../../shared/enums/tipo-Pessoa.enum';
import { PessoasListagemResponse } from '../../../shared/interfaces/api/pessoa-listagem.interface';
import { PessoaEndereco } from '../../../shared/interfaces/api/pessoa-endereco.interface';
import { PessoaContato } from '../../../shared/interfaces/api/pessoa-contato.interface';
import { PessoaDadoBancario } from '../../../shared/interfaces/api/pessoa-dado-bancario.interface';

@Injectable({
  providedIn: 'root'
})
export class PessoaService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  listar(tipoPessoa: TipoPessoa, razaoSocial: string, nomeFantasia: string, cnpj: string): Observable<PessoasListagemResponse> {
    const params = [
      `tipoPessoa=${tipoPessoa}`,
      `razaoSocial=${encodeURIComponent(razaoSocial)}`,
      `nomeFantasia=${encodeURIComponent(nomeFantasia)}`,
      `cnpj=${encodeURIComponent(cnpj)}`
    ].join('&');

    const url = `${this.API_BASE}/pessoas?${params}`;

    return this.http.get<PessoasListagemResponse>(url);
  }

  getById(id: number): Observable<PessoaResponse> {
    return this.http.get<PessoaResponse>(`${this.API_BASE}/pessoas/${id}`);
  }

  criar(pessoa: Pessoa): Observable<PessoaResponse> {
    return this.http.post<PessoaResponse>(`${this.API_BASE}/pessoas`, pessoa);
  }

  atualizar(id: number, pessoa: Pessoa): Observable<PessoaResponse> {
    return this.http.put<PessoaResponse>(`${this.API_BASE}/pessoas/${id}`, pessoa);
  }

  deletar(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/pessoas/${id}`);
  }

  incluirEndereco(endereco: PessoaEndereco): Observable<PessoaResponse> {
    return this.http.post<PessoaResponse>(`${this.API_BASE}/pessoas/incluirEndereco`, endereco);
  }

  incluirContato(contato: PessoaContato): Observable<PessoaResponse> {
    return this.http.post<PessoaResponse>(`${this.API_BASE}/pessoas/incluirContato`, contato);
  }

  atualizarEnderecoPrincipal(idPessoa: number, idEndereco: number): Observable<PessoaResponse> {
    const url = `${this.API_BASE}/pessoas/alteraEnderecoPrincipal?idPessoa=${idPessoa}&idEndereco=${idEndereco}`;
    return this.http.post<PessoaResponse>(url, {});
  }

  incluirDadoBancario(dadoBancario: PessoaDadoBancario): Observable<PessoaResponse> {
    return this.http.post<PessoaResponse>(`${this.API_BASE}/pessoas/incluirDadoBancario`, dadoBancario);
  }

  atualizarDadoBancarioPrincipal(idPessoa: number, idDadoBancario: number): Observable<PessoaResponse> {
    const url = `${this.API_BASE}/pessoas/alteraDadoBancarioPrincipal?idPessoa=${idPessoa}&idDadoBancario=${idDadoBancario}`;
    return this.http.post<PessoaResponse>(url, {});
  }
}
