<div class="card">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">Pagamentos em Lote</h5>
      <div>
        <button class="btn btn-primary btn-sm" (click)="abrirImportar()">
          <i class="feather icon-upload"></i> Importar Excel
        </button>
      </div>
    </div>
  </div>

  <div class="p-4">
    <div class="row g-3 align-items-end">
      <div class="col-md-3">
        <label class="form-label">Data inicial</label>
        <input class="form-control form-control-sm" type="date" [formControl]="filtros.controls.dataInicial" />
      </div>
      <div class="col-md-3">
        <label class="form-label">Data final</label>
        <input class="form-control form-control-sm" type="date" [formControl]="filtros.controls.dataFinal" />
      </div>
      <div class="col-md-2">
        <label class="form-label">Usuário</label>
        <input class="form-control form-control-sm" type="text" placeholder="Usuário" [formControl]="filtros.controls.usuario" />
      </div>
      <div class="col-md-2">
        <label class="form-label">Status</label>
        <app-select-simples [opcoes]="opcoesStatus" [valorSelecionado]="filtros.controls.status.value || ''" (mudouValor)="onStatusChange($event)"></app-select-simples>
      </div>
      <div class="col-md-2 d-flex gap-2">
        <button class="btn btn-outline-secondary btn-sm w-100" (click)="buscar()">Buscar</button>
        <button class="btn btn-light btn-sm w-100" (click)="limpar()">Limpar</button>
      </div>
    </div>
  </div>

  <div class="p-4 pt-0">
    <app-tabela-padrao [columnDefs]="columnDefs" [rowData]="lotes()" [pagination]="true"></app-tabela-padrao>
  </div>
</div>

