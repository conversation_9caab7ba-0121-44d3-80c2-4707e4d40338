import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { PessoasResponse, PessoaResponse, Pessoa } from '../../../shared/interfaces/api/pessoa.interface';
import { TipoPessoa } from '../../../shared/enums/tipo-Pessoa.enum';
import { PessoasListagemResponse } from '../../../shared/interfaces/api/pessoa-listagem.interface';
import { PessoaEndereco } from '../../../shared/interfaces/api/pessoa-endereco.interface';
import { PessoaContato } from '../../../shared/interfaces/api/pessoa-contato.interface';
import { ContatoTipo } from '../../../shared/interfaces/api/contato-tipo.interface';
import { ContatoTipoTelefone } from '../../../shared/interfaces/api/contato-tipo-telefone.interface';

@Injectable({
  providedIn: 'root'
})
export class ContatoService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  atualizar(id: number, contato: PessoaContato): Observable<PessoaContato> {
    return this.http.put<PessoaContato>(`${this.API_BASE}/contatos/${id}`, contato);
  }

  deletar(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/contatos/${id}`);
  }

  tipos(): Observable<ContatoTipo[]> {
    return this.http.get<ContatoTipo[]>(`${this.API_BASE}/contatos/tipos`);
  }

  tiposTelefones(): Observable<ContatoTipoTelefone[]> {
    return this.http.get<ContatoTipoTelefone[]>(`${this.API_BASE}/contatos/tiposTelefones`);
  }
}