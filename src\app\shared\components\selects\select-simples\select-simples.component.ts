import { Component, EventEmitter, Input, Output } from '@angular/core';
import { OpcaoSelect } from '../select.enum';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-select-simples',
  imports: [CommonModule, FormsModule],
  templateUrl: './select-simples.component.html',
  styleUrl: './select-simples.component.scss'
})
export class SelectSimplesComponent {

  @Input() opcoes: OpcaoSelect[] = [];
  @Input() valorSelecionado?: string | number;
  @Input() textoPadrao: string = 'Selecione uma opção';
  @Input() mostrarTextoPadrao: boolean = true; 

  @Output() mudouValor = new EventEmitter<string | number>();

  onChange(event: Event) {
    const valor = (event.target as HTMLSelectElement).value;
    this.mudouValor.emit(valor);
  }

}
