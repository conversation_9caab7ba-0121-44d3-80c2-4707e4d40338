import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { PessoasResponse, PessoaResponse, Pessoa } from '../../../shared/interfaces/api/pessoa.interface';
import { TipoPessoa } from '../../../shared/enums/tipo-Pessoa.enum';
import { PessoasListagemResponse } from '../../../shared/interfaces/api/pessoa-listagem.interface';
import { PessoaEndereco } from '../../../shared/interfaces/api/pessoa-endereco.interface';

@Injectable({
  providedIn: 'root'
})
export class EnderecoService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  atualizar(id: number, endereco: PessoaEndereco): Observable<PessoaEndereco> {
    return this.http.put<PessoaEndereco>(`${this.API_BASE}/enderecos/${id}`, endereco);
  }

  deletar(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/enderecos/${id}`);
  }
}
