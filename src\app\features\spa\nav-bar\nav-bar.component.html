<header>
  <div class="m-header" [style.display]="this.headerStyle">
    <a class="mobile-menu mobile-menu-posicao" id="mobile-collapse1" href="javascript:" (click)="this.NavCollapsedMob.emit()"><span></span></a>
    <a [routerLink]="['/pagina-inicial/']" >
      <!-- <div class="b-bg">
        <img src="assets/images/zin-branco.svg" alt="Zin Logo" class="b-logo" />
      </div>
      <span class="b-title">ZinPagsss</span> -->
      <!-- <div class="b-bg">    
      <img src="assets/images/zinpag-branco.svg" alt="Zin Logo" class="b-logo" />
    </div> -->
    <div class="b-bg">    
      <img src="assets/images/zinpag-branco.svg" alt="Zin Logo" class="b-logo" />
    </div>
    </a>
    

  </div>
  <a class="mobile-menu" [ngClass]="{ on: this.menuClass }" id="mobile-header" href="javascript:" (click)="toggleMobOption()">
    <i class="feather icon-more-horizontal"></i>
  </a>
  <div class="collapse navbar-collapse px-4" [style.display]="this.collapseStyle">
    <app-nav-left class="me-auto" [style.display]="this.headerStyle" />
    <app-nav-right class="ms-auto" />
  </div>
</header>
<div class="pc-menu-overlay" (click)="closeMenu()" (keydown)="handleKeyDown($event)" tabindex="0"></div>
