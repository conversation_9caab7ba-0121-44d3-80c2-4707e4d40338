import { TipoDescricaoDialogo } from "../../enums/dialogos/tipo-descricao-dialogo.enum";
import { ConfiguracaoDialogoLista } from "../../utils/dialogo-descricao-lista.util";
import { BotaoConfig } from "./botao-config.interface";
import { SafeHtml } from '@angular/platform-browser';

export interface DadosDialogoNotificacao<T = any> {
  titulo?: string;
  descricao?: string | SafeHtml | Array<string>;
  corHeader?: string; 
  mostrarBotaoCancelar?: boolean;
  botaoConfirmar?: BotaoConfig;
  botaoCancelar?: BotaoConfig;
  dados?: T;
}

export interface DadosDialogoNotificacaoLista<T = any>  extends DadosDialogoNotificacao{
  configCss?: Partial<ConfiguracaoDialogoLista>;
}