import { CommonModule } from '@angular/common';
import { Component, OnInit, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DialogoService } from '../../../core/services/dialogos/dialogo.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MockDataService } from '../../../shared/services/mock-data.service';

@Component({
	selector: 'app-atualizacao-status-veiculo',
	imports: [CommonModule, ReactiveFormsModule],
	templateUrl: './atualizacao-status-veiculo.component.html',
	styleUrls: ['./atualizacao-status-veiculo.component.scss'],
})
export class AtualizacaoStatusVeiculoComponent implements OnInit {
	formulario: FormGroup;
	isLoading = signal(false);
	errorMessage = '';

	statusOptions = [
		// { valor: 0, descricao: 'Desconhecido' },
		// { valor: 100, descricao: 'Não Informado' },
		// { valor: 200, descricao: 'Sem contato com a oficina' },
		{ valor: 300, descricao: 'Em reparos' },
		{ valor: 400, descricao: 'Reparos suspensos por falta de peça' },
		{ valor: 500, descricao: 'Reparos pendentes rodando com o segurado' },
		{ valor: 600, descricao: 'Reparos concluídos aguardando retirada pelo segurado' },
		{ valor: 700, descricao: 'Reparos concluídos rodando com o segurado' },
	];

	private fb = inject(FormBuilder);
	private dialogoService = inject(DialogoService);
	private route = inject(ActivatedRoute);
	private mockDataService = inject(MockDataService);
	private router = inject(Router);


	private idAgregador: string = '';
	private idAtivo: string = '';

	constructor() {
		this.formulario = this.fb.group({
			status: [null, Validators.required],
		});
	}

	ngOnInit(): void {
		// Get URL parameters
		this.route.queryParams.subscribe((params) => {
			this.idAgregador = params['idAgregador'];
			this.idAtivo = params['idAtivo'];
		});
	}

	enviar(): void {
		if (!this.formulario.valid) {
			return;
		}

		this.isLoading.set(true);
		const novoStatus = this.formulario.get('status')?.value;
		const statusLabel = this.statusOptions.find((opt) => opt.valor === novoStatus * 1);

		// Atualiza o status usando o serviço
		this.mockDataService.atualizaStatusVeiculo(this.idAgregador, this.idAtivo, statusLabel!).subscribe({
			next: () => {
				this.isLoading.set(false);
				this.dialogoService
					.dialogoNotificacao({
						titulo: 'Status do Veículo Atualizado!',
						descricao: `Status atualizado para "${statusLabel?.descricao}" com sucesso.`,
						mostrarBotaoCancelar: false,
						botaoConfirmar: {
							texto: 'OK',
							classe: 'btn-primary',
							icone: 'fa fa-check',
							habilitaIcone: true,
						},
					})
					.subscribe(()=>{
						this.router.navigate(['/movimentacoes/detalhes'], { queryParams: { idMovimentacao: this.idAgregador } });
					});
			},
			error: (error: Error) => {
				this.isLoading.set(false);
				this.errorMessage = error?.message || 'Erro ao atualizar o status do veículo.';
				console.error('Erro ao atualizar:', error);
			},
		});
	}
}
