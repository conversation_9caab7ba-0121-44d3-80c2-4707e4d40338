import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'cnpj',
  standalone: true
})
export class CnpjPipe implements PipeTransform {
  transform(value: string | null | undefined): string {
    if (!value) return '';
    
    // Remove any non-digit character
    const cnpj = value.replace(/\D/g, '');
    
    // Check if the CNPJ has 14 digits
    if (cnpj.length !== 14) return value;
    
    // Format CNPJ as XX.XXX.XXX/XXXX-XX
    return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  }
}
