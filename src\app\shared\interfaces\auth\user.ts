export interface ClienteInfo {
  id: string;
  nome_fantasia: string;
  cnpj: string;
  base_dados: string;
  tipo_agregador: number;
}

export interface DadosUsuario {
  token: string;
  refreshToken: string;
  expiration: Date;
  email: string;
  name: string;
  permission: string;
  clienteId: string;
  clientes: ClienteInfo[];
  clienteNomeFantasia: string;
  clienteCnpj: string;
  clienteBaseDados: string;
  tokenExp: Date;
}
