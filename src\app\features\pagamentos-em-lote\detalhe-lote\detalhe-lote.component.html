<!-- Breadcrumb -->
<div class="d-flex align-items-center mb-3">
  <button class="btn btn-outline-secondary btn-sm me-3" (click)="voltarParaLista()">
    <i class="fas fa-arrow-left me-1"></i> Voltar
  </button>
  <h4 class="mb-0">Detalhe do Lote #{{ loteId() }}</h4>
</div>

<!-- Loading state for header -->
@if (carregando()) {
  <div class="text-center py-4">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Carregando...</span>
    </div>
  </div>
} @else if (detalheLote()) {
  <!-- Header Section -->
  <app-card cardTitle="Informações do Lote" blockClass="mb-4">
    <div class="row">
      <div class="col-md-6">
        <div class="row mb-2">
          <div class="col-4"><strong>ID do Lote:</strong></div>
          <div class="col-8">{{ detalheLote()!.id }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Data/Hora:</strong></div>
          <div class="col-8">{{ detalheLote()!.dataProcessamento | date:'dd/MM/yyyy HH:mm' }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Usuário:</strong></div>
          <div class="col-8">{{ detalheLote()!.usuarioProcessamento }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Arquivo:</strong></div>
          <div class="col-8">{{ detalheLote()!.nomeArquivo }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Status:</strong></div>
          <div class="col-8">
            <span class="badge {{ getStatusLoteBadgeClass(detalheLote()!.status) }}">
              {{ getStatusLoteLabel(detalheLote()!.status) }}
            </span>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row mb-2">
          <div class="col-4"><strong>Lidos:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalLinhasLidas }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Programados:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalProgramados }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Liquidados:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalLiquidados }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Criados:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalCriados }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Ignorados:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalIgnorados }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Duplicados:</strong></div>
          <div class="col-8">{{ detalheLote()!.totalDuplicados }}</div>
        </div>
        <div class="row mb-2">
          <div class="col-4"><strong>Erros:</strong></div>
          <div class="col-8 text-danger">{{ detalheLote()!.totalErros }}</div>
        </div>
      </div>
    </div>
    
    <div class="row mt-3">
      <div class="col-12">
        <button 
          class="btn btn-success" 
          (click)="baixarArquivo()" 
          [disabled]="carregandoDownload()">
          @if (carregandoDownload()) {
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
          } @else {
            <i class="fas fa-download me-2"></i>
          }
          Baixar arquivo (.xls)
        </button>
      </div>
    </div>
  </app-card>

  <!-- Filters Section -->
  <app-card cardTitle="Filtros" blockClass="mb-4">
    <div class="row">
      <div class="col-md-2">
        <label class="form-label">Status da linha</label>
        <app-select-simples 
          [opcoes]="opcoesStatusLinha" 
          [valorSelecionado]="filtros.controls.statusLinha.value || ''" 
          (mudouValor)="onStatusLinhaChange($event)">
        </app-select-simples>
      </div>
      <div class="col-md-2">
        <label class="form-label">Operação aplicada</label>
        <app-select-simples 
          [opcoes]="opcoesOperacao" 
          [valorSelecionado]="filtros.controls.operacaoAplicada.value || ''" 
          (mudouValor)="onOperacaoChange($event)">
        </app-select-simples>
      </div>
      <div class="col-md-2">
        <label class="form-label">NF</label>
        <input 
          class="form-control form-control-sm" 
          type="text" 
          placeholder="Número da NF" 
          [formControl]="filtros.controls.numeroNF" />
      </div>
      <div class="col-md-3">
        <label class="form-label">CNPJs</label>
        <input 
          class="form-control form-control-sm" 
          type="text" 
          placeholder="CNPJ Pagador ou Favorecido" 
          [formControl]="filtros.controls.cnpjs" />
      </div>
      <div class="col-md-3 d-flex gap-2 align-items-end">
        <button class="btn btn-outline-secondary btn-sm" (click)="buscar()" [disabled]="carregandoLinhas()">
          @if (carregandoLinhas()) {
            <span class="spinner-border spinner-border-sm me-1" role="status"></span>
          }
          Buscar
        </button>
        <button class="btn btn-light btn-sm" (click)="limpar()">Limpar</button>
      </div>
    </div>
  </app-card>

  <!-- Processed Lines Table -->
  <app-card cardTitle="Linhas Processadas" blockClass="p-0">
    @if (carregandoLinhas()) {
      <div class="text-center py-4">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Carregando linhas...</span>
        </div>
      </div>
    } @else {
      <div class="p-4 pt-0">
        @if (linhasProcessadas().length === 0) {
          <div class="text-center py-4">
            <p class="text-muted mb-0">Nenhuma linha encontrada para os filtros selecionados.</p>
          </div>
        } @else {
          <app-tabela-padrao 
            [columnDefs]="columnDefs" 
            [rowData]="linhasProcessadas()" 
            [pagination]="true"
            [paginationPageSize]="pageSize()">
          </app-tabela-padrao>
          
          <!-- Pagination Info -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Mostrando {{ (currentPage() - 1) * pageSize() + 1 }} a 
              {{ Math.min(currentPage() * pageSize(), totalItems()) }} de 
              {{ totalItems() }} registros
            </div>
            <div>
              <button 
                class="btn btn-sm btn-outline-secondary me-2" 
                [disabled]="currentPage() === 1"
                (click)="currentPage.set(currentPage() - 1); carregarLinhasProcessadas()">
                Anterior
              </button>
              <span class="me-2">Página {{ currentPage() }} de {{ totalPages() }}</span>
              <button 
                class="btn btn-sm btn-outline-secondary" 
                [disabled]="currentPage() === totalPages()"
                (click)="currentPage.set(currentPage() + 1); carregarLinhasProcessadas()">
                Próxima
              </button>
            </div>
          </div>
        }
      </div>
    }
  </app-card>
}
