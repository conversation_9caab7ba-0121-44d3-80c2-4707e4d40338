export interface NavigationItem {
	id: string;
	title: string;
	type: 'item' | 'collapse' | 'group';
	translate?: string;
	icon?: string;
	hidden?: boolean;
	url?: string;
	classes?: string;
	exactMatch?: boolean;
	external?: boolean;
	target?: boolean;
	breadcrumbs?: boolean;

	children?: NavigationItem[];
}
export const NavigationItems: NavigationItem[] = [
	{
		id: 'navigation',
		title: 'Navegação',
		type: 'group',
		icon: 'icon-navigation',
		children: [
			// {
			//   id: 'movimentacoes',
			//   title: 'Movimentações',
			//   type: 'item',
			//   url: '/movimentacoes',
			//   icon: 'feather icon-file-minus',
			//   classes: 'nav-item',
			// },
			{
				id: 'movimentacoes',
				title: 'Movimentações',
				type: 'item',
				url: '/movimentacoes',
				icon: 'feather icon-file-minus',
				classes: 'nav-item',
			},
			{
				id: 'pagamentos',
				title: 'Pagamentos',
				type: 'collapse',
				icon: 'assets/icon/crm/dollar-sign.svg', // Este é o identificador para usar o DollarSignIconComponent
				classes: 'nav-item',
				children: [
					{
						id: 'pagamentos-listagem',
						title: 'Listagem',
						type: 'item',
						url: '/pagamentos',
						icon: 'feather icon-list',
						classes: 'nav-item',
					},
					{
						id: 'pagamentos-lotes',
						title: 'Pagamentos em Lote',
						type: 'item',
						url: '/pagamentos/lotes',
						icon: 'feather icon-upload',
						classes: 'nav-item',
					},
				],
			},
			{
				id: 'ressarcimentos',
				title: 'Ressarcimentos',
				type: 'item',
				url: '/ressarcimentos',
				icon: 'feather icon-trending-down',
				classes: 'nav-item',
			},
			{
				id: 'relatorios',
				title: 'Relatórios',
				type: 'collapse',
				icon: 'feather icon-file-text',
				classes: 'nav-item',
				children: [
					{
						id: 'relatorio-periodo',
						title: 'Consolidado por Período',
						type: 'item',
						url: '/relatorios/periodo',
						icon: 'feather icon-file-text',
						classes: 'nav-item',
					},
					{
						id: 'relatorio-status',
						title: 'Consolidado por Status',
						type: 'item',
						url: '/relatorios/status',
						icon: 'feather icon-file-text',
						classes: 'nav-item',
					},
				],
			},
			{
				id: 'cadastros',
				title: 'Cadastros',
				type: 'collapse',
				icon: 'feather icon-file-plus',
				classes: 'nav-item',
				children: [
					{
						id: 'cadastro-pessoa-juridica',
						title: 'Pessoa Jurídica',
						type: 'item',
						url: '/cadastros/pessoajuridica/listagem',
						icon: 'feather icon-file-text',
						classes: 'nav-item',
					}
				]
			},
			// {
			//   id: 'painel-principal',
			//   title: 'Painel Principal',
			//   type: 'item',
			//   url: '/painel-principal',
			//   icon: 'feather icon-trending-down',
			//   classes: 'nav-item',
			// },
			// {
			//   id: 'painel-principal-teste',
			//   title: 'Painel Principal Teste ',
			//   type: 'item',
			//   url: '/painel-principal-teste',
			//   icon: 'feather icon-trending-down',
			//   classes: 'nav-item',
			// },
			{
				id: 'regras-processamento',
				title: 'Regras de Processamento',
				type: 'item',
				url: '/regras-processamento',
				icon: 'feather icon-settings',
				classes: 'nav-item',
			},
		],
	},
];
