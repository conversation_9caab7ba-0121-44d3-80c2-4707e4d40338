import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-nav-search',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './nav-search.component.html',
  styleUrls: ['./nav-search.component.scss'],
})
export class NavSearchComponent implements OnDestroy {
  private searchInterval?: ReturnType<typeof setInterval>;
  searchWidth: number;
  searchWidthString: string;

  constructor() {
    this.searchWidth = 0;
    this.searchWidthString = '0px';
  }

  searchOn() {
    // Limpa qualquer intervalo existente
    this.clearSearchInterval();

    const searchElement = document.querySelector('#main-search');
    if (!searchElement) return;

    searchElement.classList.add('open');
    this.searchInterval = setInterval(() => {
      if (this.searchWidth >= 170) {
        this.clearSearchInterval();
        return;
      }
      this.searchWidth = this.searchWidth + 30;
      this.searchWidthString = this.searchWidth + 'px';
    }, 35);
  }

  searchOff() {
    // Limpa qualquer intervalo existente
    this.clearSearchInterval();

    this.searchInterval = setInterval(() => {
      if (this.searchWidth <= 0) {
        const searchElement = document.querySelector('#main-search');
        if (searchElement) {
          searchElement.classList.remove('open');
        }
        this.clearSearchInterval();
        return;
      }
      this.searchWidth = this.searchWidth - 30;
      this.searchWidthString = this.searchWidth + 'px';
    }, 35);
  }

  private clearSearchInterval(): void {
    if (this.searchInterval) {
      clearInterval(this.searchInterval);
      this.searchInterval = undefined;
    }
  }

  ngOnDestroy(): void {
    this.clearSearchInterval();
  }
}
