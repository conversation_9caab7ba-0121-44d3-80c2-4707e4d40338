<div class="dialogo-upload" 
     (dragover)="onDragOver($event)" 
     (dragleave)="onDragLeave($event)"
     (drop)="onFileDropped($event)"
     [class.dragging]="isDragging()">

    <h2 class="dialog-title">Carregar Arquivo</h2>

  
  <div class="upload-area" (click)="fileInput.click()">
    <mat-icon>cloud_upload</mat-icon>
    <p>Arraste arquivos aqui ou clique para selecionar</p>
    <input type="file" 
           multiple 
           (change)="onFileSelected($event)" 
           [style.display]="'none'"
           #fileInput>
    <button mat-button 
            color="primary" 
            (click)="fileInput.click()">
      Selecionar arquivos
    </button>
  </div>

  @if (files().length > 0) {
    <div class="files-preview">
      <h3>Arquivos selecionados</h3>
      <mat-list>
        @for (file of files(); track file.name) {
          <mat-list-item>
            <mat-icon matListItemIcon>description</mat-icon>
            <span matListItemTitle>{{ file.name }}</span>
            <span matListItemLine>{{ file.size | fileSize }}</span>
          </mat-list-item>
        }
      </mat-list>
    </div>
  }

  <div class="actions">
    <button class="btn btn-outline-secondary btn-sm"  (click)="cancel()">Cancelar</button>
    <button class="btn btn-primary btn-sm"
            [disabled]="!files().length"
            (click)="upload()">
      Enviar
    </button>
  </div>
</div>
