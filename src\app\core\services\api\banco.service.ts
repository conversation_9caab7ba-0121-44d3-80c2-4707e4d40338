import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { BancosResponse } from '../../../shared/interfaces/api/banco.interface';
import { BancoTipoConta } from '../../../shared/interfaces/api/banco-tipo-conta.interface';

@Injectable({
  providedIn: 'root'
})
export class BancoService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  listar(): Observable<BancosResponse> {
    return this.http.get<BancosResponse>(`${this.API_BASE}/bancos`);
  }

  tiposConta(): Observable<BancoTipoConta[]> {
      return this.http.get<BancoTipoConta[]>(`${this.API_BASE}/bancos/tiposConta`);
    }
}