<ng-scrollbar class="n-scrollbar" exclude="'#mobile-collapse1'" visibility="hover">
  <div class="navbar-content">
    <ul class="nav pcoded-inner-navbar" (clickOutside)="fireOutClick()">
      @for (item of navigations; track item) {
        @if (item.type === 'group') {
          <app-nav-group [item]="item" />
        }
      }
    </ul>
  </div>
  <div class="selecao-empresa">
    <label class="pb-1">Selecione o cliente</label>
    <app-select-simples
      [opcoes]="cliente()"
      [valorSelecionado]="clienteSelecionado()"
      [mostrarTextoPadrao]="false"
      (mudouValor)="onClienteChange($event)">
    </app-select-simples>
  </div>
  <div class="versao" [ngClass]="{ 'versao-fechado': menuCollapsed() }">
    <label for="version" disabled class="pe-auto">v{{ currentApplicationVersion }}</label>
  </div>
</ng-scrollbar>
