import { themeAlpine } from 'ag-grid-community';

export function criarConfigTema(isDarkMode: string) {
  return themeAlpine.withParams({
    accentColor: 'var(--bs-gray-200)',
    oddRowBackgroundColor: 'var(--bs-gray-200)',
    backgroundColor: 'var(--pc-table-background)',
    borderColor: 'var(--bs-border-color)',
    browserColorScheme: isDarkMode === 'dark' ? 'dark' : 'light',
    wrapperBorder: false,
    chromeBackgroundColor: {
      ref: 'foregroundColor',
      mix: 0.07,
      onto: 'backgroundColor'
    },
    fontFamily: {
      googleFont: 'Public Sans, sans-serif'
    },
    headerFontFamily: {
      googleFont: 'Public Sans, sans-serif'
    },
    foregroundColor: 'var(--pc-text-color)',
    rowHoverColor: 'var(--pc-table-header-background-150)',
    iconButtonHoverColor: 'var(--bs-table-color)',
    headerBackgroundColor: 'var(--pc-table-header-background-300)',
    menuBackgroundColor: 'var(--pc-table-header-background)',
    headerRowBorder: true,
    rowBorder: true,
    fontSize: 12,
    dataFontSize: 12,
    cellHorizontalPadding: 10,
    headerFontSize: 12,
    headerTextColor: 'var(--pc-text-color)',
    spacing: '8.3px'
  });
}
