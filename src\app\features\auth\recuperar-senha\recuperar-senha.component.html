<div class="auth-wrapper">
	<div class="auth-content">
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					<img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
				</div>
				<h3 class="mb-4"><PERSON>cupera<PERSON></h3>
				<p class="" role="status">Informe o email para recuperar a senha.</p>
				<form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
					<div class="input-group mb-3">
						<input
							type="email"
							class="form-control"
							placeholder="Email"
							formControlName="email"
							[class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
						/>
						<div class="invalid-feedback" *ngIf="loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched">
							Email é obrigatório
						</div>
						<div class="invalid-feedback" *ngIf="loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched">
							Digite um email válido
						</div>
					</div>

					<div class="alert alert-danger" *ngIf="errorMessage">
						<i class="feather icon-alert-circle"></i>
						{{ errorMessage }}
					</div>

					<button type="submit" class="btn btn-primary mb-4" [disabled]="loginForm.invalid || isLoading">
						<span *ngIf="!isLoading">Enviar</span>
						<span *ngIf="isLoading">
							<span class="spinner-border spinner-border-sm me-2" role="status"></span>
							Enviando...
						</span>
					</button>
				</form>

				<p class="mb-0 text-muted">
					já tem uma conta?
					<a [routerLink]="['/login']">Entrar</a>
				</p>
			</div>
		</div>
	</div>
</div>
