table {
  th,
  td {
    text-align: center;
    vertical-align: middle;
  }
}

.btn-icon {
  width: 35px;
  height: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0 5px;
}

button.btn:disabled {
  background-color: #d6d6d6;
  border-color: #d6d6d6;
  color: #888;
  opacity: 0.7;
}

// Collapsible interface styles
.expand-collapse-btn {
  transition: transform 0.2s ease-in-out;
  color: #6c757d;

  &:hover {
    color: #495057;
    transform: scale(1.1);
  }

  &:focus {
    box-shadow: none;
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
}

.configuration-header {
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }
}

.rules-count-badge {
  font-size: 0.75rem;
  font-weight: 500;
}

// Smooth transition for rule rows
tr[style*="display: none"] {
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}

tr[style*="display: table-row"] {
  transition: opacity 0.2s ease-in-out;
  opacity: 1;
}

hr{
  margin-top: 30px;
  margin-bottom: 30px;
}