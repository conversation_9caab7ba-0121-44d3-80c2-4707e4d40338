<div class="auth-wrapper">
	<div class="auth-content">
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					<img src="assets/images/zin-azul.svg" alt="Zin Logo" class="auth-logo" />
				</div>
				<h3 class="mb-4"><PERSON><PERSON><PERSON></h3>
				<form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
					<div class="input-group mb-3">
						<input
							type="text"
							class="form-control"
							placeholder="Usuário"
							formControlName="username"
							[class.is-invalid]="loginForm.get('username')?.invalid && loginForm.get('username')?.touched"
						/>
						<div class="invalid-feedback" *ngIf="loginForm.get('username')?.hasError('required') && loginForm.get('username')?.touched">
							Nome é obrigatório
						</div>
						<!-- <div
              class="invalid-feedback"
              *ngIf="
                loginForm.get('username')?.hasError('') &&
                loginForm.get('username')?.touched
              "
            >
              Digite um nome válido
            </div> -->
					</div>
					<div class="input-group mb-3">
						<input
							type="email"
							class="form-control"
							placeholder="Email"
							formControlName="email"
							[class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
						/>
						<div class="invalid-feedback" *ngIf="loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched">
							Email é obrigatório
						</div>
						<div class="invalid-feedback" *ngIf="loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched">
							Digite um email válido
						</div>
					</div>
					<div class="input-group mb-4">
						<input
							[type]="hidePassword ? 'password' : 'text'"
							class="form-control"
							placeholder="Password"
							formControlName="password"
							[class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
						/>
						<button class="btn btn-outline-secondary" type="button" (click)="hidePassword = !hidePassword">
							<i [class]="hidePassword ? 'feather icon-eye-off' : 'feather icon-eye'"></i>
						</button>
						<div class="invalid-feedback" *ngIf="loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched">
							Senha é obrigatória
						</div>
						<div class="invalid-feedback" *ngIf="loginForm.get('password')?.hasError('minlength') && loginForm.get('password')?.touched">
							Senha deve ter pelo menos 6 caracteres
						</div>
					</div>

					<div class="alert alert-danger" *ngIf="errorMessage">
						<i class="feather icon-alert-circle"></i>
						{{ errorMessage }}
					</div>

					<button type="submit" class="btn btn-primary mb-4" [disabled]="loginForm.invalid || isLoading">
						<span *ngIf="!isLoading">Cadastrar</span>
						<span *ngIf="isLoading">
							<span class="spinner-border spinner-border-sm me-2" role="status"></span>
							Cadastrando...
						</span>
					</button>
				</form>

				<p class="mb-0 text-muted">
					já tem uma conta?
					<a [routerLink]="['/login']">Entrar</a>
				</p>
			</div>
		</div>
	</div>
</div>
