export enum TipoAgregador
{
	'Desconhecido' = 0,
	'Sinistro' = 100,
}
 
export enum TipoPessoa
{
	'Desconhecido' = 0,
	'Fisica' = 100,
	'Juridica' = 200,
	'ClienteEmpresa' = 201
}
 
export enum TipoDocumento
{
	'Desconhecido' = 0,
	'NotaFiscalVenda' = 100,
	'NotaFiscalDevolucao' = 110,
	'Outro' = 900,
}
 
export enum TipoMovimentoItem
{
	'Desconhecido' = 0,
	'Autorizacao' = 100,
	'Exclusao' = 200,
	'Outro' = 900
}