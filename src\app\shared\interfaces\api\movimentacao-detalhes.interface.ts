export enum TipoMovimento {
    Autorizacao = "Autorização",
    Exclusao = "Exclusão",
    Venda = "Venda",
    Devolucao = "Devolucao"
}


export interface Fornecedor {
    nomeFantasia: string;
    razaoSocial: string;
    cnpj: string;
    dadosBancarios?: {
        banco: string;
        codigoBanco: string | null;
        agencia: string;
        conta: string;
        tipoConta: string;
    };
}

export interface Oficina {
    nome: string;
}

export interface Ativo {
    placa: string;
    modelo: string;
    ano: number;
    cor: string | null;
    situacaoReparo: string;
}

export interface ValoresConsolidados {
    aPagar: number;
    pago: number;
    aRessarcir: number;
    ressarcido: number;
}

export interface Documento {
    id: number;
    tipo: number;
    numero: string | null;
    dataEmissao: string;
    valor: number;
    serie: string;
    chave: string;
}

export interface DocumentoVersao {
    tipoDocumento: number;
    numero: string;
}

export interface VersaoItem {
    idItemVersao: number;
    fornecedor: {
        nome: string;
    };
    tipoMovimento: string;
    dataAutorizacao: string;
    documentos: DocumentoVersao[];
    quantidade: number;
    valorTotal: number;
    dataEntrega: string | null;
    divergencias: any[];
    condicoes: any[];
}

export interface Item {
    idItem: number;
    codigo: string;
    descricao: string;
    valorAtualizado: number;
    divergencias: boolean;
    versoes: VersaoItem[];
    condicoes: any[];
}

export interface LiquidacaoPagamento {
    id: number;
    dataPagamento: string;
    valor: number;
    temComprovante: boolean;
    documentoId: number;
}

export interface Pagamento {
    id: number;
    dataCriacao: string;
    valorTotal: number;
    valorAPagar: number;
    dataProgramada: string;
    situacao: number;
    liquidacoes: LiquidacaoPagamento[];
}

export interface LiquidacaoRessarcimento {
    id: number;
    dataRessarcimento: string;
    valor: number;
    temComprovante: boolean;
    documentoId: number;
}

export interface Ressarcimento {
    id: number;
    dataCriacao: string;
    valorTotal: number;
    valorARessarcir: number;
    dataProgramada: string;
    situacao: number;
    liquidacoes: LiquidacaoRessarcimento[];
}

export interface MovimentacoesDetalhes {
    idAgregador: number;
    idMovimentacao: number;
    numeroSinistro: string;
    fornecedor: Fornecedor;
    oficina: Oficina;
    ativo: Ativo;
    valoresConsolidados: ValoresConsolidados;
    documentos: Documento[];
    itens: Item[];
    pagamentos: Pagamento[];
    ressarcimentos: Ressarcimento[];
}
