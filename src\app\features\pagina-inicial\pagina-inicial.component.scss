@import 'src/scss/_variables.scss';

// Responsividade para os blocos .composto
.composto {
	display: flex;
	flex-direction: row;
	gap: 0px;
	width: 100%;
	flex-wrap: wrap;
	padding: 1rem 1rem 1rem 1rem;
}

.composto-itens {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;
}

// Estilos para os cards de dados do painel principal teste
.composto-item {
	background: linear-gradient(135deg, #fbfcfd 60%, #ffffff 100%);
	border-radius: 0px;
	box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.1);
	padding: 12px 18px 18px 18px;
	//   min-width: 180px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #383838;
	font-family: 'Open Sans', sans-serif !important;
	//   transition: transform 0.15s, box-shadow 0.15s;
	cursor: pointer;
	position: relative;
	overflow: hidden;

	&:hover ~ .card-principal {
		background: linear-gradient(135deg, #f0f1f3 60%, #ffffff 100%);
		box-shadow: 0 8px 28px 0 rgba(0, 0, 0, 0.18);
		transition: background-color 0.3s ease;
	}
	
	

}


.modern-pie-chart-container {
	display: contents;
}

.modern-pie-chart-container svg {
	margin-right: 30px;
}

// .composto-item:hover {
//   transform: translateY(-4px) scale(1.03);
//   box-shadow: 0 8px 28px 0 rgba(59,140,255,0.18);
// }

.composto-item .data-title {
	font-size: 1rem;
	font-weight: 200;
	margin-bottom: 3px;
	text-shadow: 0 2px 8px #0002;
}

.composto-item .data-quantidade,
.composto-item .data-valor {
	font-size: 1.05rem;
	margin-bottom: 2px;
	font-weight: 200;
}

.composto-item .data-valor {
	font-size: 1.2rem;
	font-weight: 400;
	margin-top: 2px;
}

.composto-item.tres {
    width: calc(50% - 8px);  /* 50% width minus half the gap */
}

.composto-item.dois {
    width: calc(50% - 8px);  /* 50% width minus half the gap */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .composto-item.tres,
    .composto-item.dois {
        width: 100%;  /* Full width on smaller screens */
    }
}

// Cores de destaque para cada tipo
.composto-item.exclusao {
	border-left: 5px solid;
	border-left-color: $info-color;
	//   background: linear-gradient(135deg, #6d0000 24%, #ff8fb1 129%);
}
.composto-item.pago-com-exclusao {
	border-left: 5px solid;
	border-left-color: $purple-color;
	//   background: linear-gradient(135deg, #3a8f41 60%, #9afdb8 100%);

}
.composto-item.pago-sem-exclusao {
	border-left: 5px solid;
	border-left-color: $info-color-light;
	//   background: linear-gradient(135deg, #3a8f41 60%, #9afdb8 100%);
}
.composto-item.apagar {
	border-left: 5px solid;
	border-left-color: $success-color;
	//   background: linear-gradient(135deg, #3b8cff 60%, #8ca7eb 100%);
}
.composto-item.incluidas {
	border-left: 5px solid;
	border-left-color: $success-color;
	//   background: linear-gradient(135deg, #6d0000 24%, #ff8fb1 129%);
}
.composto-item.nfpendentes {
	border-left: 5px solid;
	border-left-color: $success-color-light;
	//   background: linear-gradient(135deg, #3a8f41 60%, #9afdb8 100%);
}
.composto-item.realizados {
	border-left: 5px solid;
	border-left-color: $purple-color;
	//   background: linear-gradient(135deg, #3b8cff 60%, #8ca7eb 100%);
}
.composto-item.pendentes {
	border-left: 5px solid;
	border-left-color: $purple-color-light;

	//   background: linear-gradient(135deg, #3b8cff 60%, #8ca7eb 100%);
}

// Estilos para o gráfico de pizza
.pie-chart-exclusao {
	stroke: $info-color;
}
.pie-chart-pago-com-exclusao{
	stroke: $purple-color;
}

.pie-chart-pago-sem-exclusao {
	stroke: $info-color-light;
}


.pie-chart-apagar {
	stroke: $success-color;
}

.pie-chart-incluidas {
	stroke: $success-color;
}

.pie-chart-pendentes {
	stroke: $success-color-light;
}
.pie-chart-realizados {
	stroke: $purple-color;
}
.pie-chart-ressarcimentos-pendentes {
	stroke: $purple-color-light;
}

// Estilos para o gráfico de pizza legendas
.pie-chart-field-legend {
	// color: #383838 !important;
	justify-self: center;
	display: flex;
	flex-direction: column;
	gap: 8px;
	width: 48%;
}

.pie-chart-icon-legend {
	display: inline-block;
	width: 14px;
	height: 14px;
	border-radius: 3px;
	margin-right: 6px;
}

.pie-chart-icon-legend.exclusao {
	background:  $info-color;

	&.hover-effect {
		width: 15px;
		height: 15px;
		box-shadow: 0px 1px 10px 2px rgba($info-color, 0.4)
	}
}

.pie-chart-icon-legend.pago-sem-exclusao {
	background: $info-color-light;

	&.hover-effect {
		width: 15px;
		height: 15px;
		box-shadow: 0px 1px 10px 2px rgba($info-color-light, 0.4)
	}
}
.pie-chart-icon-legend.pago-com-exclusao {
	background: $purple-color;

	&.hover-effect {
		width: 15px;
		height: 15px;
		box-shadow: 0px 1px 10px 2px rgba($purple-color, 0.4)
	}
}
.pie-chart-icon-legend.apagar {
	background: $success-color;

	&.hover-effect {
		width: 15px;
		height: 15px;
		box-shadow: 0px 1px 10px 2px rgba($success-color, 0.4)
	}
}
.pie-chart-icon-legend.incluidas {
	background: $success-color;
}
.pie-chart-icon-legend.pendentes {
	background: $success-color-light;
}
.pie-chart-icon-legend.realizados {
	background: $purple-color;
}
.pie-chart-icon-legend.ressarcimentos-pendentes {
	background: $purple-color-light;
}

.dashboard-primeira-linha {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1rem;
}

.progress-value {
	color: #fff;
	font-size: 0.95rem;
	font-weight: 600;
	width: 100%;
	text-align: center;
	display: block;
	line-height: 1.7;
}

// .dashboard-card{
// 	display: fle;
// }

.color-label {
	font-family: 'Open Sans', sans-serif;
}

.card-principal-ressarcimento  {
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding-bottom: 1.5rem;
	background: #fff;
	width: 32%;
	height: -webkit-fill-available;
	transition: all 0.3s ease;

	&.hover-effect {
		background-color: rgba($purple-color, 0.02);
		box-shadow: 0 8px 28px 0 rgba(0, 0, 0, 0.18);
	}
}

// .hover-effect-success {
// 		background-color: rgba($success-color, 0.1) !important;
// 		box-shadow: 0 8px 28px 0 rgba(0, 0, 0, 0.18) !important;
		
// 	}

// 	.hover-effect-purple {
// 		background-color: rgba($purple-color, 0.1);
// 		box-shadow: 20px 20px 28px 0 rgba(0, 0, 0, 0.18);
		
// 	}

.card-principal-pagar  {
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding-bottom: 1.5rem;
	background: #fff;
	width: 32%;
	height: -webkit-fill-available;
	transition: all 0.3s ease;

	&.hover-effect {
		background-color: rgba($success-color, 0.02);
		box-shadow: 0 8px 28px 0 rgba(0, 0, 0, 0.18);
	}
}

.card-principal  {
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding-bottom: 1.5rem;
	background: #fff;
	width: 32%;
	height: -webkit-fill-available;
	transition: all 0.3s ease;

	
}

.modern-card-autorizados {
	background: $theme-color-blue;
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif !important;
	min-width: 49%;
}

.modern-card-pagar {
	background: $theme-color-green;
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif;
	min-width: 49%;
}

.modern-card-ressarcimento {
	background: $theme-color-purple;
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif;
	min-width: 49%;
}

.modern-card-cancelamentos {
	// background: linear-gradient(45deg, rgb(255 203 21 / 89%), #ecb96296);
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif;
	min-width: 49%;
}
.modern-card-pagos {
	// background: linear-gradient(45deg, #04bb66, rgb(0 255 8 / 39%));
	// background: linear-gradient(45deg, #51aeff, #69edff);
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif !important;
	min-width: 49%;
}

.modern-card-ressarcimentos {
	// background: linear-gradient(45deg, #fd4f4fe3, #ffa6a6);
	// background: linear-gradient(45deg, #d2ab01, rgb(255 207 0 / 39%));
	border-radius: 0px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
	padding: 1.5rem 1.5rem 1.5rem 1.5rem;
	margin: 0px;
	font-family: 'Open Sans', sans-serif !important;
	min-width: 49%;
}

.modern-header {
	display: flex;
	align-items: center;
	justify-self: center;
	justify-content: space-between;
	// margin-bottom: 1.2rem;
}

.modern-title-autorizado {
	font-size: 1.2rem;
	font-weight: 400;
	color: #ffffffe0;
}

.modern-title {
	font-size: 1.2rem;
	font-weight: 400;
	color: #383838;
}

.modern-icon {
	font-size: 3.5rem;
	color: #fff;
}

.modern-icon-pagos {
	font-size: 3.5rem;
	color: #05cd0f;
}

.modern-icon-cancelados {
	font-size: 3.5rem;
	color: #f8cf60;
}

.modern-icon-ressarcimentos {
	font-size: 3.5rem;
	color: #f87e60;
}

.modern-value-row {
	display: flex;
	align-items: center;
	flex-direction: row;
	gap: 0.5rem;
	margin-bottom: 1.5rem;
}

// .modern-value-column {
// 	display: flex;
// 	align-items: flex-start;
// 	flex-direction: column;
// 	// gap: 0.5rem;
// 	// margin-bottom: 1.5rem;
// 	// margin-left: 1.5rem;
// }

.modern-value {
	font-size: 2rem;
	font-weight: 400;
	color: #fff;
}

.modern-value-new {
	font-size: 2rem;
	font-weight: 400;
	color: #646464;
}
.modern-up {
	color: #22c55e;
	font-size: 1.3rem;
}
.modern-earnings {
	margin-top: -6px;
	color: #888;
	font-size: 0.95rem;
	font-weight: 500;
	margin-bottom: 1.1rem;
}
.modern-progress-labels {
	display: flex;
	justify-content: space-between;
	font-size: 0.95rem;
	margin-bottom: 0.2rem;
}
.modern-label {
	color: #383838;
	font-weight: 500;
}
.modern-label-autorizados {
	color: #ffffffe0;
	font-weight: 500;
}

.modern-progress-bar-container {
	background: #d4d4d4;
	border-radius: 8px;
	height: 8px;
	width: 100%;
	display: flex;
	overflow: hidden;
	// margin-bottom: 1.2rem;
	box-shadow: 0 1px 4px rgba(80, 80, 80, 0.04);
}

.modern-leads-list {
	margin-top: 0.7rem;
}
.modern-lead-row {
	display: flex;
	justify-content: space-between;
	font-size: 1.05rem;
	color: #383838;
	padding: 0.18rem 0;
	border-bottom: 1px solid #f3f3f3;
}

.modern-lead-row-autorizados {
	display: flex;
	justify-content: space-between;
	font-size: 1.05rem;
	color: #ffffffe0;
	padding: 0.18rem 0;
	// border-bottom: 1px solid #f3f3f3;
}

.modern-lead-row:last-child {
	border-bottom: none;
}

.composto-itens {
	display: flex;
	flex-direction: row;
	gap: 10px;
	justify-content: center;
	margin-bottom: 24px;
	width: 100%;
	text-align: center;
}

.header-itens {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 12px;
}

.select-periodo {
	display: flex;
	justify-content: flex-start;
	margin-bottom: 16px;
	align-items: center;
	gap: 8px;
}

@media (max-width: 1550px) {
	.composto {
		flex-direction: column;
		gap: 0px;
		align-items: stretch;
	}

	span.modern-value {
		font-size: 1.2rem;
	}

	.composto-itens {
		flex-direction: column;

		.dois,
		.tres {
			width: 100% !important;
		}
	}

	.card-principal {
		height: -webkit-fill-available;
	}
}

@media (max-width: 1205px) {
	span.modern-value {
		font-size: 2rem;
	}

	.dashboard-primeira-linha {
		flex-direction: column;
		justify-content: space-between;
	}

	.card-principal, .card-principal-ressarcimento,  .card-principal-pagar {
		width: 100%;
		margin-bottom: 1rem;
	}
}
