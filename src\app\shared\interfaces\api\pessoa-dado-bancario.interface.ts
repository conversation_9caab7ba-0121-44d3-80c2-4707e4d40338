export interface PessoaDadoBancario {
	idPessoa?: number;
	id_dados_bancarios: number;
	idBanco: number;
	nomeBanco: string;
	agencia: string;
	agenciaDv?: string;
	conta: string;
	contaDv: string;
	tipoConta: number;
	titular: string;
	cpfCnpjTitular: string;
	pix?: string;
	principal: boolean;
}

export type PessoaDadoBancarioResponse = PessoaDadoBancario;
export type PessoaDadosBancariosResponse = PessoaDadoBancario[];