export interface LoginRequest {
  username: string;
  password: string;
}

export interface CadastrarRequest extends LoginRequest {
  email: string;
}

export interface RecuperarSenhaRequest {
  email: string;
}

export interface ConviteNovoUsuarioRequest {
  email: string;
  clientes: string[];
}

export interface CadastroNovoUsuarioRequest {
  username: string;
  newUsername: string;
  token: string;
  newPassword: string;
}

export interface novaSenhaRequest {
  email: string;
  resetCode: string;
  newPassword: string;
}

export interface ConfirmacaoEmailRequest {
  username: string;
}

export interface verificaCodigoEmailRequest {
  username: string;
  code: string;
}

export interface refrashTokenRequest {
  refreshToken: string;
}
