<div
	class="modal-header"
	[ngClass]="{
		'header-info': corHeader === 'info',
		'header-erro': corHeader === 'erro',
		'header-sucesso': corHeader === 'sucesso',
		'header-padrao': corHeader === 'padrao' || !corHeader,
	}"
>
	<h4 class="modal-title">{{ titulo }}</h4>
</div>
<div class="modal-body">
  @for (item of descricao; track item; let i = $index) {
	<ul [class]="configListCss.estiloContainer">
		<div [class]="configListCss.estiloItem">
			<span [class]="configListCss.icone + ' ' + configListCss.corIcone" style="margin-right: 8px !important"></span>
			<span>{{ item }}</span>
    	</div>
	</ul>
    
  }
</div>
<div class="modal-footer" [class.single-button]="!mostrarBotaoCancelar">
	@if (mostrarBotaoCancelar) {
		<button type="button" class="btn" [class]="botaoCancelar.classe" [disabled]="botaoCancelar.disabled" (click)="onClick(false)">
			@if (botaoCancelar.icone) {
				<i [class]="botaoCancelar.icone" [class.spin]="botaoCancelar.loading"></i>
			}
			{{ botaoCancelar.texto }}
		</button>
	}

	<button type="button" class="btn" [class]="botaoConfirmar.classe" [disabled]="botaoConfirmar.disabled" (click)="onClick(true)">
		@if (botaoConfirmar.icone) {
			<i [class]="botaoConfirmar.icone" [class.spin]="botaoConfirmar.loading"></i>
		}
		{{ botaoConfirmar.texto }}
	</button>
</div>
