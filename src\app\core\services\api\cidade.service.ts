import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { CidadesResponse } from '../../../shared/interfaces/api/cidade.interface';

@Injectable({
  providedIn: 'root'
})
export class CidadeService {
  private readonly API_BASE = environment.apiUrl;

  constructor(private http: HttpClient) {}

  listar(idEstado: number): Observable<CidadesResponse> {
    return this.http.get<CidadesResponse>(`${this.API_BASE}/cidades?idEstado=${idEstado}`);
  }
}