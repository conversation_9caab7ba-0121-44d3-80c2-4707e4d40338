import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { TwoFactorComponent } from './two-factor.component';
import { AuthService } from '../../../core/auth/services/auth.service';

describe('TwoFactorComponent', () => {
  let component: TwoFactorComponent;
  let fixture: ComponentFixture<TwoFactorComponent>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['send2FACode', 'verify2FACode']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      queryParams: of({ email: '<EMAIL>' })
    };

    await TestBed.configureTestingModule({
      imports: [TwoFactorComponent, ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TwoFactorComponent);
    component = fixture.componentInstance;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with code field', () => {
    expect(component.doisFatForm.get('code')).toBeTruthy();
    expect(component.doisFatForm.get('code')?.hasError('required')).toBeTruthy();
  });

  it('should send 2FA code on init', () => {
    mockAuthService.confirmacaoEmail.and.returnValue(of({ 
      result: { success: true, message: 'Code sent', statusCode: 200 } 
    }));
    
    component.ngOnInit();
    
    expect(mockAuthService.confirmacaoEmail).toHaveBeenCalledWith({ email: '<EMAIL>' });
  });

  it('should verify 2FA code on form submit', () => {
    mockAuthService.verificaCodigoEmail.and.returnValue(of({ 
      result: { 
        success: true, 
        message: 'Verified', 
        statusCode: 200,
        token: 'test-token',
        refreshToken: 'refresh-token',
        expiration: new Date()
      } 
    }));
    
    component.username.set('<EMAIL>');
    component.doisFatForm.patchValue({ code: '123456' });
    
    component.onSubmit();
    
    expect(mockAuthService.verificaCodigoEmail).toHaveBeenCalledWith({ 
      email: '<EMAIL>', 
      code: '123456' 
    });
  });

  it('should handle error on code verification', () => {
    mockAuthService.verificaCodigoEmail.and.returnValue(throwError(() => ({ 
      error: { message: 'Invalid code' } 
    })));
    
    component.username.set('<EMAIL>');
    component.doisFatForm.patchValue({ code: '123456' });
    
    component.onSubmit();
    
    expect(component.mensagemErro).toBe('Invalid code');
  });

  it('should navigate to login when no email provided', () => {
    mockActivatedRoute.queryParams = of({});
    
    component.ngOnInit();
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should navigate to dashboard on successful verification', (done) => {
    mockAuthService.verificaCodigoEmail.and.returnValue(of({ 
      result: { 
        success: true, 
        message: 'Verified', 
        statusCode: 200,
        token: 'test-token',
        refreshToken: 'refresh-token',
        expiration: new Date()
      } 
    }));
    
    component.username.set('<EMAIL>');
    component.doisFatForm.patchValue({ code: '123456' });
    
    component.onSubmit();
    
    // Aguarda o setTimeout
    setTimeout(() => {
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/pagina-inicial']);
      done();
    }, 1600);
  });
});
