import { Component, inject } from '@angular/core';

// bootstrap import
import { Ngb<PERSON><PERSON>down, NgbDropdownConfig, NgbDropdownMenu, NgbDropdownToggle } from '@ng-bootstrap/ng-bootstrap';
import { Router, RouterModule } from '@angular/router';
import { DadosUsuario } from '../../../../shared/interfaces/auth/user';
import { AuthService } from '../../../../core/auth/services/auth.service';
import { DialogoService } from '../../../../core/services/dialogos/dialogo.service';

@Component({
	selector: 'app-nav-right',
	imports: [NgbDropdown, NgbDropdownMenu, NgbDropdownToggle, RouterModule],
	templateUrl: './nav-right.component.html',
	styleUrls: ['./nav-right.component.scss'],
	providers: [NgbDropdownConfig],
})
export class NavRightComponent {
	dadosUsuario: DadosUsuario | null = null;

	constructor(
		private authService: AuthService,
		private router: Router,
		private dialogoService: DialogoService
	) {
		const config = inject(NgbDropdownConfig);

		this.dadosUsuario = this.authService.getUsuarioAtual();
		config.placement = 'bottom-right';
	}

	logout(): void {
		this.dialogoService
			.dialogoNotificacao({
				titulo: 'Sair do Sistema',
				descricao: 'Deseja realmente sair?',
				mostrarBotaoCancelar: true,
				botaoConfirmar: {
					texto: 'Sim',
					classe: 'btn-danger',
				},
				botaoCancelar: {
					texto: 'Cancelar',
					classe: 'btn-outline-secondary',
					icone: 'feather icon-x',
				},
			})
			.subscribe((result) => {
				if (result.confirmado) {
					this.authService.logout().subscribe({
						next: () => {
							this.router.navigate(['/login']);
						},
						error: () => {
							this.router.navigate(['/login']);
						},
					});
				}
			});
	}
}
