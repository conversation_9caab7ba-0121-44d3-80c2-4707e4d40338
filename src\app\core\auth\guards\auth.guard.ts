import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.authService.estaAutenticado$.pipe(
      take(1),
      map((estaAutenticado) => {
        if (estaAutenticado) {
          return true;
        } else {
          this.router.navigate(['/login']);
          return false;
        }
      })
    );
  }
}

@Injectable({
  providedIn: 'root',
})
export class LoginGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.authService.estaAutenticado$.pipe(
      take(1),
      map((estaAutenticado) => {
        if (!estaAutenticado) {
          return true;
        } else {
          this.router.navigate(['/pagina-inicial']);
          return false;
        }
      })
    );
  }
}
