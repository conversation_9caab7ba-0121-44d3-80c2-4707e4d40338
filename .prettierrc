{"printWidth": 150, "tabWidth": 4, "useTabs": true, "semi": true, "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "arrowParens": "always", "endOfLine": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "bracketSameLine": false, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "jsxSingleQuote": false, "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "chainOperatorBreakBefore": false, "methodChainBreakBefore": true, "plugins": [], "overrides": [{"files": "*.ts", "options": {"printWidth": 100, "parser": "typescript"}}]}