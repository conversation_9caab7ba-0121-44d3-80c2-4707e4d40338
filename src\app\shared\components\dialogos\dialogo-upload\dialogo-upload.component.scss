.dialogo-upload {
  padding: 1.5rem;
  min-width: 300px;
  max-width: 600px;
  width: 100%;

  .dialog-title {
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--theme-color);
  }

  &.dragging .upload-area {
    border-color: var(--primary-color, #1e88e5);
    background-color: rgba(30, 136, 229, 0.05);
  }
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: var(--primary-color, #1e88e5);
  }

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #666;
    margin-bottom: 1rem;
  }

  p {
    margin: 0.5rem 0;
    color: #666;
  }
}

.files-preview {
  margin-top: 1.5rem;
  border-top: 1px solid #eee;
  padding-top: 1rem;

  h3 {
    margin: 0 0 1rem;
    font-size: 1rem;
    color: #333;
  }

  mat-list {
    max-height: 200px;
    overflow-y: auto;
  }

  mat-list-item {
    height: auto !important;
    margin-bottom: 0.5rem;

    mat-icon {
      color: #666;
    }
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

@media (max-width: 600px) {
  .dialogo-upload {
    padding: 1rem;
    min-width: 250px;
  }

  .upload-area {
    padding: 1.5rem;

    mat-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
    }
  }

  .files-preview mat-list {
    max-height: 150px;
  }
}
